name: Build and Deploy Zyka POS

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to build (e.g., v1.2.0)'
        required: true
        default: 'v1.2.0'

permissions:
  contents: write
  actions: read
  security-events: write
  deployments: write

env:
  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
  CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]
        include:
          - os: windows-latest
            platform: win
            ext: exe
          - os: macos-latest
            platform: mac
            ext: dmg
          - os: ubuntu-latest
            platform: linux
            ext: AppImage

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install system dependencies (Linux)
        if: matrix.os == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libnss3-dev libatk-bridge2.0-dev libdrm2 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Debug environment
        run: |
          echo "Node version: $(node --version)"
          echo "NPM version: $(npm --version)"
          echo "OS: ${{ matrix.os }}"
          echo "Platform: ${{ matrix.platform }}"

      - name: Build Electron app
        run: npm run draft
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          DEBUG: electron-builder

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: zyka-pos-${{ matrix.platform }}
          path: |
            release/*.exe
            release/*.dmg
            release/*.AppImage
            release/*.yml
            release/*.json
            release/*.blockmap
          retention-days: 30

  deploy-to-cloudflare:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v') && always()

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts

      - name: Prepare update files
        run: |
          mkdir -p updates-dist/releases
          
          # Copy all release files
          find artifacts -name "*.exe" -exec cp {} updates-dist/releases/ \;
          find artifacts -name "*.dmg" -exec cp {} updates-dist/releases/ \;
          find artifacts -name "*.AppImage" -exec cp {} updates-dist/releases/ \;
          find artifacts -name "*.yml" -exec cp {} updates-dist/ \;
          find artifacts -name "*.json" -exec cp {} updates-dist/ \;
          find artifacts -name "*.blockmap" -exec cp {} updates-dist/releases/ \;
          
          # Copy Cloudflare Pages configuration
          cp _headers updates-dist/
          cp _redirects updates-dist/
          
          # Create index.html for the updates site
          cat > updates-dist/index.html << 'EOF'
          <!DOCTYPE html>
          <html>
          <head>
              <title>Zyka POS Updates</title>
              <meta charset="utf-8">
              <style>
                  body { font-family: Arial, sans-serif; margin: 40px; }
                  .container { max-width: 800px; margin: 0 auto; }
                  .file-list { margin: 20px 0; }
                  .file-item { padding: 10px; border: 1px solid #ddd; margin: 5px 0; }
                  .version { font-weight: bold; color: #2196F3; }
              </style>
          </head>
          <body>
              <div class="container">
                  <h1>Zyka POS Updates</h1>
                  <p>This is the update server for Zyka POS application.</p>
                  <div class="file-list">
                      <div class="file-item">
                          <a href="/latest.yml">Latest Windows Version</a>
                      </div>
                      <div class="file-item">
                          <a href="/latest-mac.yml">Latest macOS Version</a>
                      </div>
                      <div class="file-item">
                          <a href="/latest-linux.yml">Latest Linux Version</a>
                      </div>
                  </div>
              </div>
          </body>
          </html>
          EOF

      - name: Install Wrangler
        run: npm install -g wrangler@latest

      - name: Deploy to Cloudflare Pages
        run: |
          echo "Deploying to Cloudflare Pages..."
          wrangler pages deploy updates-dist --project-name=zyka-pos-updates --compatibility-date=2024-01-01
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          files: |
            artifacts/**/*.exe
            artifacts/**/*.dmg
            artifacts/**/*.AppImage
            artifacts/**/*.yml
            artifacts/**/*.json
            artifacts/**/*.blockmap
          draft: false
          prerelease: false
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}


