# Cloudflare Pages Headers Configuration
# This file sets proper headers for update files

# Windows executables
/*.exe
  Content-Type: application/octet-stream
  Cache-Control: public, max-age=3600
  Access-Control-Allow-Origin: *
  X-Content-Type-Options: nosniff

# macOS disk images
/*.dmg
  Content-Type: application/octet-stream
  Cache-Control: public, max-age=3600
  Access-Control-Allow-Origin: *
  X-Content-Type-Options: nosniff

# Linux AppImages
/*.AppImage
  Content-Type: application/octet-stream
  Cache-Control: public, max-age=3600
  Access-Control-Allow-Origin: *
  X-Content-Type-Options: nosniff

# Update metadata files
/*.yml
  Content-Type: text/yaml
  Cache-Control: public, max-age=300
  Access-Control-Allow-Origin: *

/*.json
  Content-Type: application/json
  Cache-Control: public, max-age=300
  Access-Control-Allow-Origin: *

# Blockmap files for delta updates
/*.blockmap
  Content-Type: application/octet-stream
  Cache-Control: public, max-age=3600
  Access-Control-Allow-Origin: *

# All files security headers
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
