{"name": "zyka-pos-updates", "production_branch": "main", "build": {"command": "npm run build:updates", "destination_dir": "updates-dist", "root_dir": "/", "environment_variables": {"NODE_VERSION": "18", "NPM_VERSION": "latest"}}, "preview": {"command": "npm run build:updates", "destination_dir": "updates-dist", "environment_variables": {"NODE_VERSION": "18", "NPM_VERSION": "latest"}}, "deployment": {"compatibility_date": "2024-01-01", "compatibility_flags": [], "usage_model": "standard"}, "custom_domains": [], "redirects": [{"source": "/latest", "destination": "/latest.yml", "status_code": 302}, {"source": "/latest-mac", "destination": "/latest-mac.yml", "status_code": 302}, {"source": "/latest-linux", "destination": "/latest-linux.yml", "status_code": 302}], "headers": [{"source": "*.exe", "headers": {"Content-Type": "application/octet-stream", "Cache-Control": "public, max-age=3600", "Access-Control-Allow-Origin": "*", "X-Content-Type-Options": "nosniff"}}, {"source": "*.dmg", "headers": {"Content-Type": "application/octet-stream", "Cache-Control": "public, max-age=3600", "Access-Control-Allow-Origin": "*", "X-Content-Type-Options": "nosniff"}}, {"source": "*.AppImage", "headers": {"Content-Type": "application/octet-stream", "Cache-Control": "public, max-age=3600", "Access-Control-Allow-Origin": "*", "X-Content-Type-Options": "nosniff"}}, {"source": "*.yml", "headers": {"Content-Type": "text/yaml", "Cache-Control": "public, max-age=300", "Access-Control-Allow-Origin": "*"}}, {"source": "*.json", "headers": {"Content-Type": "application/json", "Cache-Control": "public, max-age=300", "Access-Control-Allow-Origin": "*"}}, {"source": "*.blockmap", "headers": {"Content-Type": "application/octet-stream", "Cache-Control": "public, max-age=3600", "Access-Control-Allow-Origin": "*"}}, {"source": "*", "headers": {"X-Frame-Options": "DENY", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "strict-origin-when-cross-origin"}}], "functions": {"directory": "functions", "compatibility_date": "2024-01-01"}}