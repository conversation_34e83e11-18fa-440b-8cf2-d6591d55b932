# Cloudflare Pages Configuration for Zyka POS Updates
# This file configures how update files are served from Cloudflare Pages

# Build settings
build:
  command: "npm run build:updates"
  publish: "updates-dist"
  environment:
    NODE_VERSION: "18"

# Headers for update files
headers:
  - source: "*.exe"
    headers:
      - key: "Content-Type"
        value: "application/octet-stream"
      - key: "Cache-Control"
        value: "public, max-age=3600"
      - key: "Access-Control-Allow-Origin"
        value: "*"
  
  - source: "*.dmg"
    headers:
      - key: "Content-Type"
        value: "application/octet-stream"
      - key: "Cache-Control"
        value: "public, max-age=3600"
      - key: "Access-Control-Allow-Origin"
        value: "*"
  
  - source: "*.AppImage"
    headers:
      - key: "Content-Type"
        value: "application/octet-stream"
      - key: "Cache-Control"
        value: "public, max-age=3600"
      - key: "Access-Control-Allow-Origin"
        value: "*"
  
  - source: "*.yml"
    headers:
      - key: "Content-Type"
        value: "text/yaml"
      - key: "Cache-Control"
        value: "public, max-age=300"
      - key: "Access-Control-Allow-Origin"
        value: "*"
  
  - source: "*.json"
    headers:
      - key: "Content-Type"
        value: "application/json"
      - key: "Cache-Control"
        value: "public, max-age=300"
      - key: "Access-Control-Allow-Origin"
        value: "*"

# Redirects for update endpoints
redirects:
  - source: "/latest"
    destination: "/latest.yml"
    status: 302
  
  - source: "/latest-mac"
    destination: "/latest-mac.yml"
    status: 302
  
  - source: "/latest-linux"
    destination: "/latest-linux.yml"
    status: 302

# Security settings
security:
  - source: "*"
    headers:
      - key: "X-Content-Type-Options"
        value: "nosniff"
      - key: "X-Frame-Options"
        value: "DENY"
      - key: "Referrer-Policy"
        value: "strict-origin-when-cross-origin"
