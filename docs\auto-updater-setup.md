# Auto-Updater Setup Guide

This guide explains how to set up and use the auto-updater system in Zyka POS.

## Overview

The auto-updater system uses `electron-updater` to provide seamless application updates. It supports:

- Automatic update checking
- Background downloading
- User notifications
- Configurable update intervals
- Support for all platforms (Windows, macOS, Linux)

## Configuration

### 1. Publishing Setup

The auto-updater is configured to use GitHub releases for distribution. Update the following in `package.json`:

```json
{
  "build": {
    "publish": [
      {
        "provider": "github",
        "owner": "your-github-username",
        "repo": "your-repo-name",
        "private": true
      }
    ]
  }
}
```

### 2. Environment Variables

For publishing releases, you'll need to set up a GitHub token:

```bash
# Set GitHub token for publishing
export GH_TOKEN="your-github-personal-access-token"
```

### 3. Code Signing (Production)

For production releases, especially on macOS and Windows, you should set up code signing:

#### Windows
```bash
export CSC_LINK="path-to-your-certificate.p12"
export CSC_KEY_PASSWORD="your-certificate-password"
```

#### macOS
```bash
export CSC_LINK="path-to-your-certificate.p12"
export CSC_KEY_PASSWORD="your-certificate-password"
export APPLE_ID="your-apple-id"
export APPLE_ID_PASS="your-app-specific-password"
```

## Usage

### Building and Publishing

1. **Build for distribution:**
   ```bash
   npm run dist
   ```

2. **Publish to GitHub releases:**
   ```bash
   npm run publish
   ```

3. **Platform-specific builds:**
   ```bash
   npm run dist:win    # Windows
   npm run dist:mac    # macOS
   npm run dist:linux  # Linux
   ```

4. **Platform-specific publishing:**
   ```bash
   npm run publish:win    # Windows
   npm run publish:mac    # macOS
   npm run publish:linux  # Linux
   ```

### Development Testing

For testing the auto-updater in development:

1. Create a test release on GitHub
2. Update the version in `package.json` to be lower than the test release
3. Run the application in development mode
4. The auto-updater will detect the newer version and offer to update

### User Interface

The auto-updater includes two main UI components:

1. **UpdateNotification**: Shows update status and progress
2. **UpdateSettings**: Allows users to configure update preferences

#### Integration Example

```tsx
import UpdateNotification from './components/UpdateNotification';
import UpdateSettings from './components/UpdateSettings';

function App() {
  return (
    <div>
      {/* Your app content */}
      <UpdateNotification />
      
      {/* In settings page */}
      <UpdateSettings />
    </div>
  );
}
```

## API Reference

### Main Process APIs

- `autoUpdaterService.checkForUpdates()` - Manually check for updates
- `autoUpdaterService.downloadUpdate()` - Download available update
- `autoUpdaterService.quitAndInstall()` - Install downloaded update
- `autoUpdaterService.getStatus()` - Get current update status
- `autoUpdaterService.getConfig()` - Get update configuration
- `autoUpdaterService.updateConfig(config)` - Update configuration

### Renderer Process APIs

Available through `window.electronAPI`:

- `updaterCheckForUpdates()` - Check for updates
- `updaterDownloadUpdate()` - Download update
- `updaterQuitAndInstall()` - Install update
- `updaterGetStatus()` - Get status
- `updaterGetConfig()` - Get configuration
- `updaterUpdateConfig(config)` - Update configuration
- `onUpdaterEvent(callback)` - Listen for update events

## Configuration Options

The auto-updater can be configured with the following options:

```typescript
interface UpdateConfig {
  enabled: boolean;              // Enable/disable auto-updates
  checkOnStartup: boolean;       // Check for updates on app startup
  checkInterval: number;         // Check interval in hours (0 = never)
  allowPrerelease: boolean;      // Include pre-release versions
  autoDownload: boolean;         // Automatically download updates
  autoInstallOnAppQuit: boolean; // Install updates when app quits
  notifyUser: boolean;           // Show update notifications
}
```

## Events

The auto-updater emits the following events:

- `update-checking` - Started checking for updates
- `update-available` - Update is available
- `update-not-available` - No update available
- `update-error` - Error occurred
- `update-download-progress` - Download progress update
- `update-downloaded` - Update downloaded and ready

## Troubleshooting

### Common Issues

1. **Updates not detected:**
   - Ensure the version in `package.json` is lower than the published release
   - Check that the GitHub repository and release settings are correct
   - Verify network connectivity

2. **Download failures:**
   - Check internet connection
   - Verify GitHub release assets are properly uploaded
   - Check for firewall or proxy issues

3. **Installation failures:**
   - Ensure the application has proper permissions
   - Check for antivirus interference
   - Verify code signing certificates (for production)

### Debug Mode

To enable debug logging for the auto-updater:

```bash
# Set environment variable
export ELECTRON_UPDATER_DEBUG=true
```

## Security Considerations

1. **Code Signing**: Always sign your releases for production
2. **HTTPS**: Updates are downloaded over HTTPS by default
3. **Verification**: electron-updater verifies update signatures
4. **Permissions**: Ensure proper file system permissions for updates

## Best Practices

1. **Version Management**: Use semantic versioning (semver)
2. **Release Notes**: Include meaningful release notes
3. **Testing**: Test updates thoroughly before publishing
4. **Rollback**: Keep previous versions available for rollback
5. **User Experience**: Provide clear update notifications and progress indicators

## Platform-Specific Notes

### Windows
- Uses NSIS installer by default
- Supports delta updates for smaller download sizes
- Requires code signing for production

### macOS
- Uses DMG format by default
- Requires notarization for macOS 10.15+
- Supports both Intel and Apple Silicon

### Linux
- Uses AppImage format by default
- No code signing required
- Supports auto-updates through AppImageUpdate
