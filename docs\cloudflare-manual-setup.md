# Manual Cloudflare Pages Setup via Web Dashboard

## 🌐 Step-by-Step Manual Setup (No CLI Required)

### **Step 1: Create Cloudflare Account**

1. **Go to Cloudflare**
   - Visit: https://cloudflare.com/
   - Click **"Sign Up"** (or **"Log In"** if you have an account)

2. **Create Free Account**
   - Enter your email and password
   - Verify your email address
   - Complete account setup

### **Step 2: Access Cloudflare Pages**

1. **Login to Cloudflare Dashboard**
   - Go to: https://dash.cloudflare.com/
   - Login with your credentials

2. **Navigate to Pages**
   - In the left sidebar, click **"Pages"**
   - Or go directly to: https://dash.cloudflare.com/pages

### **Step 3: Create New Pages Project**

1. **Click "Create a project"**
   - You'll see options to connect to Git or upload directly

2. **Choose "Connect to Git"**
   - Click **"Connect to Git"**
   - Select **"GitHub"**

3. **Authorize GitHub Connection**
   - Click **"Authorize Cloudflare Pages"**
   - Login to your GitHub account if prompted
   - Grant necessary permissions

4. **Select Repository**
   - Find and select: **"EriaSoftware/zyka_pos"**
   - Click **"Begin setup"**

### **Step 4: Configure Build Settings**

1. **Project Name**
   - Set to: `zyka-pos-updates`

2. **Production Branch**
   - Set to: `main` (or your default branch)

3. **Build Settings**
   - **Framework preset**: None (or Custom)
   - **Build command**: `npm run build:updates`
   - **Build output directory**: `updates-dist`
   - **Root directory**: `/` (leave empty for repository root)

4. **Environment Variables** (Optional)
   - Click **"Add variable"** if needed
   - Add: `NODE_VERSION` = `18` (to match your current version)

5. **Click "Save and Deploy"**

### **Step 5: Wait for Initial Build**

1. **Monitor Build Process**
   - You'll see the build logs in real-time
   - First build might take 5-10 minutes

2. **Build Status**
   - ✅ **Success**: Your site is deployed
   - ❌ **Failed**: Check build logs for errors

3. **Get Your URL**
   - Once successful, you'll get: `https://zyka-pos-updates.pages.dev`

### **Step 6: Configure Custom Settings**

1. **Go to Project Settings**
   - Click on your project name
   - Click **"Settings"** tab

2. **Build & Deployments**
   - Verify build command: `npm run build:updates`
   - Verify output directory: `updates-dist`

3. **Environment Variables**
   - Add any needed variables
   - `NODE_VERSION`: `18`

4. **Custom Domains** (Optional)
   - Click **"Custom domains"**
   - Add your domain if you have one
   - Follow DNS setup instructions

### **Step 7: Set Up Automatic Deployments**

1. **GitHub Integration**
   - Go to **"Settings"** → **"Builds & deployments"**
   - Ensure **"Automatic git deployments"** is enabled

2. **Branch Configuration**
   - Production branch: `main`
   - Preview branches: All branches (optional)

3. **Build Triggers**
   - Deployments will trigger on:
     - Push to main branch
     - New tags (if configured)

## 🔧 Manual File Upload Method (Alternative)

If GitHub integration doesn't work, you can upload files manually:

### **Step 1: Prepare Files Locally**
```powershell
# Build your project
npm run build
npm run dist
npm run build:updates
```

### **Step 2: Upload via Dashboard**
1. **Create Pages Project**
   - Choose **"Upload assets"** instead of Git
   - Project name: `zyka-pos-updates`

2. **Upload Files**
   - Drag and drop the `updates-dist` folder
   - Or click **"Select from computer"**
   - Upload all files from `updates-dist/`

3. **Deploy**
   - Click **"Deploy site"**
   - Wait for deployment to complete

## 🔑 Get API Credentials for GitHub Actions

### **Step 1: Get API Token**

1. **Go to API Tokens**
   - Click your profile icon (top right)
   - Select **"My Profile"**
   - Click **"API Tokens"** tab

2. **Create Custom Token**
   - Click **"Create Token"**
   - Use **"Custom token"** template

3. **Configure Token**
   - **Token name**: `Zyka POS Pages Deploy`
   - **Permissions**:
     - `Cloudflare Pages:Edit`
     - `Account:Read`
   - **Account Resources**: Include your account
   - Click **"Continue to summary"**
   - Click **"Create Token"**

4. **Copy Token**
   - Copy the generated token (starts with letters/numbers)
   - **Save it securely** - you won't see it again!

### **Step 2: Get Account ID**

1. **Find Account ID**
   - In the right sidebar of any Cloudflare dashboard page
   - Look for **"Account ID"** in the API section
   - Copy the Account ID (32-character string)

### **Step 3: Add to GitHub Secrets**

1. **Go to GitHub Repository**
   - Visit: https://github.com/EriaSoftware/zyka_pos
   - Click **"Settings"** tab
   - Click **"Secrets and variables"** → **"Actions"**

2. **Add Secrets**
   - Click **"New repository secret"**
   - Add `CLOUDFLARE_API_TOKEN` with your API token
   - Add `CLOUDFLARE_ACCOUNT_ID` with your Account ID

## 🧪 Test Your Setup

### **Step 1: Check Your Site**
1. **Visit Your Update Server**
   - Go to: `https://zyka-pos-updates.pages.dev`
   - Should show your update server homepage

2. **Test Update Endpoints**
   - `https://zyka-pos-updates.pages.dev/latest.yml`
   - `https://zyka-pos-updates.pages.dev/latest-mac.yml`
   - `https://zyka-pos-updates.pages.dev/latest-linux.yml`

### **Step 2: Test GitHub Actions**
```powershell
# Create a test release
git tag v1.2.1-test
git push origin v1.2.1-test
```

1. **Monitor GitHub Actions**
   - Go to your repository → **"Actions"** tab
   - Watch the workflow run
   - Check for any errors

2. **Verify Deployment**
   - Check if new files appear on your Cloudflare Pages site
   - Verify update metadata is updated

## 🔧 Manual Deployment Process

### **For Each New Release:**

1. **Build Locally**
   ```powershell
   npm run build
   npm run dist
   npm run build:updates
   ```

2. **Upload to Cloudflare Pages**
   - Go to your Pages project dashboard
   - Click **"Upload assets"** or use Git push
   - Upload contents of `updates-dist/` folder

3. **Verify Deployment**
   - Check your update server URL
   - Test download links
   - Verify metadata files

## 📋 Configuration Checklist

### ✅ **Cloudflare Pages Setup**
- [ ] Account created and verified
- [ ] Pages project created (`zyka-pos-updates`)
- [ ] GitHub repository connected
- [ ] Build settings configured
- [ ] First deployment successful
- [ ] Update server accessible

### ✅ **GitHub Integration**
- [ ] API token created with correct permissions
- [ ] Account ID copied
- [ ] GitHub Secrets added (`CLOUDFLARE_API_TOKEN`, `CLOUDFLARE_ACCOUNT_ID`)
- [ ] GitHub Actions workflow tested
- [ ] Automatic deployments working

### ✅ **Testing**
- [ ] Update server responds (https://zyka-pos-updates.pages.dev)
- [ ] Metadata endpoints work (/latest.yml, /latest-mac.yml, /latest-linux.yml)
- [ ] Download links functional
- [ ] Auto-updater detects updates

## 🚨 Troubleshooting

### **Common Issues**

1. **Build Fails**
   - Check build logs in Cloudflare Pages dashboard
   - Verify `npm run build:updates` works locally
   - Check Node.js version in environment variables

2. **Files Not Found**
   - Verify build output directory is `updates-dist`
   - Check if `npm run build:updates` creates files
   - Ensure all files are uploaded

3. **GitHub Actions Fails**
   - Check if secrets are properly set
   - Verify API token permissions
   - Check workflow logs for specific errors

### **Getting Help**
- Cloudflare Pages documentation: https://developers.cloudflare.com/pages/
- GitHub Actions logs: Repository → Actions tab
- Cloudflare support: https://support.cloudflare.com/

## ✅ Success!

Once completed, you'll have:
- ✅ Cloudflare Pages project hosting your updates
- ✅ Automatic deployments from GitHub
- ✅ Professional update server at `https://zyka-pos-updates.pages.dev`
- ✅ Secure API token management
- ✅ Working auto-updater system

**No Node.js upgrade or Wrangler CLI required!** 🎉
