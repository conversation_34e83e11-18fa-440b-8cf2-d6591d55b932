{"name": "zyka", "version": "1.2.6", "description": "Touch-friendly POS desktop software", "main": "dist/main.js", "homepage": "./", "scripts": {"build": "webpack --mode production", "build:dev": "webpack --mode development", "start": "electron .", "dev": "webpack --mode development --watch", "electron:dev": "npm run build:dev && electron .", "pack": "electron-builder --dir", "dist": "npm run build && npm run rebuild && electron-builder", "dist:win": "npm run build && npm run rebuild && electron-builder --win", "dist:mac": "npm run build && npm run rebuild && electron-builder --mac", "dist:linux": "npm run build && npm run rebuild && electron-builder --linux", "publish": "npm run build && electron-builder --publish=always", "publish:win": "npm run build && electron-builder --win --publish=always", "publish:mac": "npm run build && electron-builder --mac --publish=always", "publish:linux": "npm run build && electron-builder --linux --publish=always", "draft": "npm run build && electron-builder --publish=never", "postinstall": "electron-builder install-app-deps", "rebuild": "npx electron-rebuild", "test:analytics": "node scripts/test-analytics.js", "test:updater": "electron scripts/test-updater.js", "test:installation": "electron scripts/test-installation.js", "build:updates": "node scripts/prepare-updates.js", "build:updates:cloudflare": "cross-env NODE_ENV=production node scripts/prepare-updates.js", "deploy:cloudflare": "node scripts/deploy-cloudflare.js", "setup:updater": "node scripts/setup-updater.js", "setup:cloudflare": "node scripts/setup-cloudflare-pages.js", "clean": "rimraf dist release node_modules/.cache", "clean:all": "rimraf dist release node_modules", "lint": "echo '<PERSON><PERSON> not configured yet'", "format": "echo 'Formatting not configured yet'"}, "keywords": ["pos", "desktop", "electron", "typescript", "touch"], "author": "Zyka Team", "license": "MIT", "build": {"appId": "com.zyka.pos", "productName": "Zyka POS", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*"], "extraResources": [{"from": "src/assets", "to": "assets", "filter": ["**/*"]}], "nodeGypRebuild": false, "buildDependenciesFromSource": false, "npmRebuild": true, "publish": [{"provider": "github", "owner": "EriaSoftware", "repo": "zyka_pos", "private": true}, {"provider": "generic", "url": "https://zyka-pos-updates.pages.dev"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/icon.ico", "verifyUpdateCodeSignature": false}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.business", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "assets/entitlements.mac.plist", "entitlementsInherit": "assets/entitlements.mac.plist"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": false, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Zyka POS"}}, "dependencies": {"axios": "^1.10.0", "dotenv": "^17.2.0", "electron-updater": "^6.6.2", "lowdb": "^7.0.1", "node-cron": "^4.2.1", "nodemailer": "^7.0.5", "react": "^19.1.0", "react-dom": "^19.1.0", "sqlite3": "^5.1.7"}, "devDependencies": {"@types/node": "^24.0.14", "@types/nodemailer": "^6.4.17", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "electron": "^37.2.3", "electron-builder": "^26.0.12", "electron-rebuild": "^3.2.9", "html-webpack-plugin": "^5.6.3", "rimraf": "^6.0.1", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.100.2", "webpack-cli": "^6.0.1"}}