const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Deploy updates to Cloudflare Pages
 * This script handles the deployment process
 */

const updatesDir = path.join(__dirname, '..', 'updates-dist');

function checkRequirements() {
  // Check if wrangler is installed
  try {
    execSync('wrangler --version', { stdio: 'ignore' });
  } catch (error) {
    console.error('Wrangler CLI not found. Please install it:');
    console.error('npm install -g wrangler');
    process.exit(1);
  }
  
  // Check if updates directory exists
  if (!fs.existsSync(updatesDir)) {
    console.error('Updates directory not found. Run "npm run build:updates" first.');
    process.exit(1);
  }
}

function deployToCloudflare() {
  console.log('Deploying to Cloudflare Pages...');
  
  try {
    // Deploy using wrangler
    const command = `wrangler pages deploy ${updatesDir} --project-name=zyka-pos-updates`;
    console.log(`Running: ${command}`);
    
    execSync(command, { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    
    console.log('\\n✅ Deployment successful!');
    console.log('\\nYour update server is now live at:');
    console.log('https://zyka-pos-updates.pages.dev');
    
  } catch (error) {
    console.error('❌ Deployment failed:', error.message);
    console.error('\\nTroubleshooting:');
    console.error('1. Make sure you are logged in to Wrangler: wrangler auth login');
    console.error('2. Check your Cloudflare account permissions');
    console.error('3. Verify the project name is correct');
    process.exit(1);
  }
}

function showInstructions() {
  console.log('\\n📋 Setup Instructions:');
  console.log('\\n1. Install Wrangler CLI (if not already installed):');
  console.log('   npm install -g wrangler');
  console.log('\\n2. Login to Cloudflare:');
  console.log('   wrangler auth login');
  console.log('\\n3. Create a new Pages project:');
  console.log('   wrangler pages project create zyka-pos-updates');
  console.log('\\n4. Run this script again to deploy');
  console.log('\\n5. (Optional) Configure custom domain in Cloudflare dashboard');
}

function main() {
  console.log('🚀 Cloudflare Pages Deployment Script');
  console.log('=====================================\\n');
  
  try {
    checkRequirements();
    deployToCloudflare();
  } catch (error) {
    if (error.message.includes('not found') || error.message.includes('not authenticated')) {
      showInstructions();
    } else {
      console.error('❌ Unexpected error:', error.message);
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { deployToCloudflare };
