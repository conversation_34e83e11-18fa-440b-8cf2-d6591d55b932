const fs = require('fs');
const path = require('path');

/**
 * Prepare update files for Cloudflare Pages deployment
 * This script organizes release files for the update server
 */

const releaseDir = path.join(__dirname, '..', 'release');
const updatesDir = path.join(__dirname, '..', 'updates-dist');

function ensureDir(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

function copyFile(src, dest) {
  if (fs.existsSync(src)) {
    fs.copyFileSync(src, dest);
    console.log(`Copied: ${path.basename(src)}`);
  }
}

function createIndexHtml() {
  const indexHtml = `<!DOCTYPE html>
<html>
<head>
    <title>Zyka POS Updates</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 40px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .logo {
            font-size: 2.5em;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            font-size: 1.1em;
        }
        .file-list {
            margin: 30px 0;
        }
        .file-item {
            padding: 15px;
            border: 1px solid #ddd;
            margin: 10px 0;
            border-radius: 5px;
            transition: background-color 0.2s;
        }
        .file-item:hover {
            background-color: #f9f9f9;
        }
        .file-name {
            font-weight: bold;
            color: #333;
        }
        .file-desc {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }
        .status {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            color: #666;
            font-size: 0.9em;
        }
        a {
            color: #2196F3;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🚀 Zyka POS</div>
            <div class="subtitle">Auto-Update Server</div>
        </div>

        <div class="status">
            ✅ Update server is online and ready to serve updates
        </div>

        <div class="file-list">
            <h2>Available Update Channels</h2>

            <div class="file-item">
                <div class="file-name">
                    <a href="/latest.yml">latest.yml</a>
                </div>
                <div class="file-desc">Windows update metadata</div>
            </div>

            <div class="file-item">
                <div class="file-name">
                    <a href="/latest-mac.yml">latest-mac.yml</a>
                </div>
                <div class="file-desc">macOS update metadata</div>
            </div>

            <div class="file-item">
                <div class="file-name">
                    <a href="/latest-linux.yml">latest-linux.yml</a>
                </div>
                <div class="file-desc">Linux update metadata</div>
            </div>
        </div>

        <div class="footer">
            <p>Powered by <a href="https://pages.cloudflare.com">Cloudflare Pages</a></p>
            <p>Zyka POS Auto-Updater System</p>
        </div>
    </div>
</body>
</html>`;

  fs.writeFileSync(path.join(updatesDir, 'index.html'), indexHtml);
  console.log('Created: index.html');

  // Verify files were created
  const createdFiles = fs.readdirSync(updatesDir);
  console.log('Files created in updates-dist:');
  createdFiles.forEach(file => console.log(`  - ${file}`));

  console.log('\\nUpdate files prepared successfully!');
  console.log(`Files are ready in: ${updatesDir}`);
}

function createMinimalUpdateServer() {
  console.log('Creating minimal update server for Cloudflare Pages...');

  // Ensure directories exist
  ensureDir(updatesDir);
  ensureDir(path.join(updatesDir, 'releases'));

  // Copy Cloudflare Pages configuration
  const configFiles = ['_headers', '_redirects'];
  configFiles.forEach(configFile => {
    const srcPath = path.join(__dirname, '..', configFile);
    if (fs.existsSync(srcPath)) {
      copyFile(srcPath, path.join(updatesDir, configFile));
    }
  });

  // Create placeholder metadata files
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
  const version = packageJson.version || '1.2.0';
  const releaseDate = new Date().toISOString();

  // Create latest.yml for Windows
  const windowsYml = `version: ${version}
files:
  - url: Zyka-POS-Setup-${version}.exe
    sha512: placeholder-sha512-hash
    size: 0
path: Zyka-POS-Setup-${version}.exe
sha512: placeholder-sha512-hash
releaseDate: '${releaseDate}'
`;

  // Create latest-mac.yml for macOS
  const macYml = `version: ${version}
files:
  - url: Zyka-POS-${version}.dmg
    sha512: placeholder-sha512-hash
    size: 0
path: Zyka-POS-${version}.dmg
sha512: placeholder-sha512-hash
releaseDate: '${releaseDate}'
`;

  // Create latest-linux.yml for Linux
  const linuxYml = `version: ${version}
files:
  - url: Zyka-POS-${version}.AppImage
    sha512: placeholder-sha512-hash
    size: 0
path: Zyka-POS-${version}.AppImage
sha512: placeholder-sha512-hash
releaseDate: '${releaseDate}'
`;

  fs.writeFileSync(path.join(updatesDir, 'latest.yml'), windowsYml);
  fs.writeFileSync(path.join(updatesDir, 'latest-mac.yml'), macYml);
  fs.writeFileSync(path.join(updatesDir, 'latest-linux.yml'), linuxYml);

  console.log('Created placeholder metadata files');

  // Create index.html and finalize
  createIndexHtml();
}

function prepareUpdates() {
  console.log('Preparing update files for Cloudflare Pages...');

  // Debug environment variables
  console.log('Environment check:');
  console.log('- CF_PAGES:', process.env.CF_PAGES);
  console.log('- CF_PAGES_BRANCH:', process.env.CF_PAGES_BRANCH);
  console.log('- CF_PAGES_COMMIT_SHA:', process.env.CF_PAGES_COMMIT_SHA);
  console.log('- NODE_ENV:', process.env.NODE_ENV);
  console.log('- CI:', process.env.CI);

  // Create directories
  ensureDir(updatesDir);
  ensureDir(path.join(updatesDir, 'releases'));

  // Check if we're in a CI/CD environment (Cloudflare Pages or any CI)
  const isCloudflarePages = process.env.CF_PAGES === '1' ||
                           process.env.CF_PAGES_BRANCH ||
                           process.env.CF_PAGES_COMMIT_SHA ||
                           process.env.NODE_ENV === 'production' ||
                           process.env.CI === 'true';

  console.log('Is Cloudflare Pages/CI environment:', isCloudflarePages);

  if (!fs.existsSync(releaseDir)) {
    console.log('Release directory not found - creating minimal update server...');
    console.log('This is normal for initial deployment or CI/CD environments.');
    createMinimalUpdateServer();
    return;
  }
  
  // Copy release files
  const files = fs.readdirSync(releaseDir);
  
  files.forEach(file => {
    const srcPath = path.join(releaseDir, file);
    const stat = fs.statSync(srcPath);
    
    if (stat.isFile()) {
      const ext = path.extname(file).toLowerCase();
      
      // Copy executables to releases folder
      if (['.exe', '.dmg', '.appimage', '.blockmap'].includes(ext)) {
        copyFile(srcPath, path.join(updatesDir, 'releases', file));
      }
      // Copy metadata files to root
      else if (['.yml', '.json'].includes(ext)) {
        copyFile(srcPath, path.join(updatesDir, file));
      }
    }
  });
  
  // Copy Cloudflare Pages configuration
  const configFiles = ['_headers', '_redirects'];
  configFiles.forEach(configFile => {
    const srcPath = path.join(__dirname, '..', configFile);
    if (fs.existsSync(srcPath)) {
      copyFile(srcPath, path.join(updatesDir, configFile));
    }
  });
  
  // Create index.html (for local builds with actual release files)
  createIndexHtml();

  console.log('\\nUpdate files prepared successfully!');
  console.log(`Files are ready in: ${updatesDir}`);
  console.log('\\nNext steps:');
  console.log('1. Deploy to Cloudflare Pages');
  console.log('2. Configure your domain (optional)');
  console.log('3. Test the update server');
}

if (require.main === module) {
  prepareUpdates();
}

module.exports = { prepareUpdates };
