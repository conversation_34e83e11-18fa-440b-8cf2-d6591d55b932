const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Automated Cloudflare Pages setup script for Zyka POS Auto-Updater
 * This script creates and configures the Cloudflare Pages project
 */

const PROJECT_NAME = 'zyka-pos-updates';
const GITHUB_REPO = 'EriaSoftware/zyka_pos';

function checkWranglerInstalled() {
  try {
    execSync('wrangler --version', { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

function checkWranglerAuth() {
  try {
    const result = execSync('wrangler auth whoami', { encoding: 'utf8' });
    return result.includes('@');
  } catch (error) {
    return false;
  }
}

function createPagesProject() {
  console.log(`🚀 Creating Cloudflare Pages project: ${PROJECT_NAME}`);
  
  try {
    // Check if project already exists
    const existingProjects = execSync('wrangler pages project list', { encoding: 'utf8' });
    if (existingProjects.includes(PROJECT_NAME)) {
      console.log(`✅ Project ${PROJECT_NAME} already exists`);
      return true;
    }
    
    // Create new project
    execSync(`wrangler pages project create ${PROJECT_NAME}`, { stdio: 'inherit' });
    console.log(`✅ Created project: ${PROJECT_NAME}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to create project: ${error.message}`);
    return false;
  }
}

function deployInitialVersion() {
  console.log('📦 Preparing initial deployment...');
  
  try {
    // Build the application first
    console.log('   Building application...');
    execSync('npm run build', { stdio: 'inherit' });
    
    // Create distribution
    console.log('   Creating distribution...');
    execSync('npm run dist', { stdio: 'inherit' });
    
    // Prepare update files
    console.log('   Preparing update files...');
    execSync('npm run build:updates', { stdio: 'inherit' });
    
    // Deploy to Cloudflare Pages
    console.log('   Deploying to Cloudflare Pages...');
    execSync(`wrangler pages deploy updates-dist --project-name=${PROJECT_NAME}`, { 
      stdio: 'inherit' 
    });
    
    console.log('✅ Initial deployment successful!');
    return true;
  } catch (error) {
    console.error(`❌ Deployment failed: ${error.message}`);
    return false;
  }
}

function configureProject() {
  console.log('⚙️ Configuring project settings...');
  
  // Note: Wrangler CLI doesn't support all configuration options
  // Some settings need to be configured manually in the dashboard
  
  console.log('📋 Manual configuration required:');
  console.log('   1. Go to Cloudflare Pages dashboard');
  console.log('   2. Select your project: ' + PROJECT_NAME);
  console.log('   3. Go to Settings → Build & deployments');
  console.log('   4. Set build command: npm run build:updates');
  console.log('   5. Set build output directory: updates-dist');
  console.log('   6. Add environment variables if needed');
  
  return true;
}

function testDeployment() {
  console.log('🧪 Testing deployment...');
  
  const testUrl = `https://${PROJECT_NAME}.pages.dev`;
  
  try {
    // Test if the site is accessible
    const testCommand = process.platform === 'win32' 
      ? `curl -s -o nul -w "%{http_code}" ${testUrl}`
      : `curl -s -o /dev/null -w "%{http_code}" ${testUrl}`;
    
    const statusCode = execSync(testCommand, { encoding: 'utf8' }).trim();
    
    if (statusCode === '200') {
      console.log('✅ Deployment test successful!');
      console.log(`   Your update server is live at: ${testUrl}`);
      return true;
    } else {
      console.log(`⚠️ Deployment test returned status: ${statusCode}`);
      console.log(`   Check your deployment at: ${testUrl}`);
      return false;
    }
  } catch (error) {
    console.log('⚠️ Could not test deployment automatically');
    console.log(`   Please manually check: https://${PROJECT_NAME}.pages.dev`);
    return false;
  }
}

function showNextSteps() {
  console.log('\\n🎉 Cloudflare Pages Setup Complete!');
  console.log('=====================================\\n');
  
  console.log('🔗 Your Update Server URLs:');
  console.log(`   Main: https://${PROJECT_NAME}.pages.dev`);
  console.log(`   Windows: https://${PROJECT_NAME}.pages.dev/latest.yml`);
  console.log(`   macOS: https://${PROJECT_NAME}.pages.dev/latest-mac.yml`);
  console.log(`   Linux: https://${PROJECT_NAME}.pages.dev/latest-linux.yml`);
  
  console.log('\\n📋 Next Steps:');
  console.log('1. Configure GitHub Secrets (see docs/github-secrets-setup.md)');
  console.log('2. Test auto-updater with your application');
  console.log('3. Create your first release:');
  console.log('   git tag v1.2.1');
  console.log('   git push origin v1.2.1');
  console.log('4. Monitor GitHub Actions for automated deployments');
  
  console.log('\\n🛠️ Management Commands:');
  console.log(`   Deploy manually: wrangler pages deploy updates-dist --project-name=${PROJECT_NAME}`);
  console.log(`   View deployments: wrangler pages deployment list --project-name=${PROJECT_NAME}`);
  console.log('   Dashboard: https://dash.cloudflare.com/');
  
  console.log('\\n📚 Documentation:');
  console.log('   Setup Guide: docs/cloudflare-pages-setup.md');
  console.log('   GitHub Secrets: docs/github-secrets-setup.md');
  console.log('   Integration: docs/integration-example.md');
}

function main() {
  console.log('🌐 Cloudflare Pages Setup for Zyka POS Auto-Updater');
  console.log('===================================================\\n');
  
  // Check prerequisites
  if (!checkWranglerInstalled()) {
    console.error('❌ Wrangler CLI not found. Please install it:');
    console.error('   npm install -g wrangler');
    process.exit(1);
  }
  
  if (!checkWranglerAuth()) {
    console.error('❌ Not authenticated with Cloudflare. Please login:');
    console.error('   wrangler auth login');
    process.exit(1);
  }
  
  console.log('✅ Prerequisites check passed');
  
  // Create and configure project
  if (!createPagesProject()) {
    console.error('❌ Failed to create Cloudflare Pages project');
    process.exit(1);
  }
  
  // Deploy initial version
  if (!deployInitialVersion()) {
    console.error('❌ Failed to deploy initial version');
    console.error('   You can try deploying manually later');
  }
  
  // Configure project settings
  configureProject();
  
  // Test deployment
  testDeployment();
  
  // Show next steps
  showNextSteps();
}

if (require.main === module) {
  main();
}

module.exports = {
  createPagesProject,
  deployInitialVersion,
  testDeployment
};
