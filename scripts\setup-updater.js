const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Setup script for Zyka POS auto-updater
 * This script configures the environment and tests the setup
 */

const GITHUB_TOKEN = '****************************************';
const GITHUB_REPO = 'https://github.com/EriaSoftware/zyka_pos';

function setEnvironmentVariable(name, value) {
  // Set environment variable for current session
  process.env[name] = value;
  
  // Create/update .env file
  const envPath = path.join(__dirname, '..', '.env');
  let envContent = '';
  
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
  }
  
  const lines = envContent.split('\\n');
  const existingIndex = lines.findIndex(line => line.startsWith(`${name}=`));
  
  if (existingIndex >= 0) {
    lines[existingIndex] = `${name}=${value}`;
  } else {
    lines.push(`${name}=${value}`);
  }
  
  fs.writeFileSync(envPath, lines.join('\\n'));
  console.log(`✅ Set ${name} in .env file`);
}

function checkGitHubAccess() {
  console.log('🔍 Checking GitHub access...');
  
  try {
    // Test GitHub API access
    const command = `curl -H "Authorization: token ${GITHUB_TOKEN}" https://api.github.com/repos/EriaSoftware/zyka_pos`;
    const result = execSync(command, { encoding: 'utf8' });
    const repoInfo = JSON.parse(result);
    
    if (repoInfo.name === 'zyka_pos') {
      console.log('✅ GitHub access confirmed');
      console.log(`   Repository: ${repoInfo.full_name}`);
      console.log(`   Private: ${repoInfo.private}`);
      return true;
    }
  } catch (error) {
    console.error('❌ GitHub access failed:', error.message);
    return false;
  }
}

function setupGitHubToken() {
  console.log('🔑 Setting up GitHub token...');
  setEnvironmentVariable('GH_TOKEN', GITHUB_TOKEN);
  setEnvironmentVariable('GITHUB_TOKEN', GITHUB_TOKEN);
}

function createCloudflareConfig() {
  console.log('☁️ Creating Cloudflare configuration...');
  
  const wranglerConfig = {
    name: 'zyka-pos-updates',
    compatibility_date: '2024-01-01',
    pages_build_output_dir: 'updates-dist'
  };
  
  const wranglerPath = path.join(__dirname, '..', 'wrangler.toml');
  const tomlContent = `name = "${wranglerConfig.name}"
compatibility_date = "${wranglerConfig.compatibility_date}"
pages_build_output_dir = "${wranglerConfig.pages_build_output_dir}"

[env.production]
name = "zyka-pos-updates"

[env.preview]
name = "zyka-pos-updates-preview"
`;
  
  fs.writeFileSync(wranglerPath, tomlContent);
  console.log('✅ Created wrangler.toml');
}

function testBuild() {
  console.log('🔨 Testing build process...');
  
  try {
    console.log('   Building application...');
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ Build successful');
    return true;
  } catch (error) {
    console.error('❌ Build failed:', error.message);
    return false;
  }
}

function showNextSteps() {
  console.log('\\n🎉 Setup Complete!');
  console.log('==================\\n');
  
  console.log('📋 Next Steps:');
  console.log('\\n1. Build and test your application:');
  console.log('   npm run build');
  console.log('   npm run dist');
  console.log('\\n2. Test the updater:');
  console.log('   npm run test:updater');
  console.log('\\n3. Prepare update files:');
  console.log('   npm run build:updates');
  console.log('\\n4. Deploy to Cloudflare Pages:');
  console.log('   npm run deploy:cloudflare');
  console.log('\\n5. Create a release:');
  console.log('   git tag v1.2.1');
  console.log('   git push origin v1.2.1');
  console.log('\\n6. Publish to GitHub:');
  console.log('   npm run publish');
  
  console.log('\\n🔗 Important URLs:');
  console.log(`   GitHub Repository: ${GITHUB_REPO}`);
  console.log('   Update Server: https://zyka-pos-updates.pages.dev');
  console.log('   Cloudflare Dashboard: https://dash.cloudflare.com/');
  
  console.log('\\n⚠️  Security Notes:');
  console.log('   - GitHub token is stored in .env file');
  console.log('   - Add .env to .gitignore to keep token secure');
  console.log('   - For production, use GitHub Secrets for CI/CD');
}

function createGitIgnore() {
  const gitignorePath = path.join(__dirname, '..', '.gitignore');
  let gitignoreContent = '';
  
  if (fs.existsSync(gitignorePath)) {
    gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
  }
  
  const entriesToAdd = [
    '.env',
    '.env.local',
    'updates-dist/',
    'release/',
    'node_modules/',
    'dist/'
  ];
  
  let modified = false;
  entriesToAdd.forEach(entry => {
    if (!gitignoreContent.includes(entry)) {
      gitignoreContent += `\\n${entry}`;
      modified = true;
    }
  });
  
  if (modified) {
    fs.writeFileSync(gitignorePath, gitignoreContent);
    console.log('✅ Updated .gitignore');
  }
}

function main() {
  console.log('🚀 Zyka POS Auto-Updater Setup');
  console.log('===============================\\n');
  
  // Setup GitHub token
  setupGitHubToken();
  
  // Check GitHub access
  if (!checkGitHubAccess()) {
    console.error('\\n❌ Setup failed: Cannot access GitHub repository');
    console.error('Please check your token and repository settings');
    process.exit(1);
  }
  
  // Create configurations
  createCloudflareConfig();
  createGitIgnore();
  
  // Test build
  if (!testBuild()) {
    console.error('\\n❌ Setup incomplete: Build test failed');
    console.error('Please fix build issues before proceeding');
    process.exit(1);
  }
  
  // Show next steps
  showNextSteps();
}

if (require.main === module) {
  main();
}

module.exports = { 
  setupGitHubToken, 
  checkGitHubAccess, 
  createCloudflareConfig 
};
