const { app, BrowserWindow } = require('electron');
const path = require('path');
const fs = require('fs');

// Test installation functionality
class InstallationTester {
  constructor() {
    this.testResults = [];
    this.userDataPath = app.getPath('userData');
  }

  async runAllTests() {
    console.log('🧪 Starting Installation Tests...\n');
    
    try {
      await this.testEnvironmentSetup();
      await this.testDatabaseInitialization();
      await this.testFilePermissions();
      await this.testEmailConfiguration();
      await this.testAutoUpdater();
      await this.testUserInterface();
      
      this.printResults();
    } catch (error) {
      console.error('❌ Installation test failed:', error);
    }
  }

  async testEnvironmentSetup() {
    console.log('📁 Testing Environment Setup...');
    
    try {
      // Test user data directory
      const userDataExists = fs.existsSync(this.userDataPath);
      this.addResult('User Data Directory', userDataExists, this.userDataPath);
      
      // Test write permissions
      const testFile = path.join(this.userDataPath, 'test-write.tmp');
      try {
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
        this.addResult('Write Permissions', true, 'Can write to user data directory');
      } catch (error) {
        this.addResult('Write Permissions', false, error.message);
      }
      
      // Test environment service
      try {
        const { getEnvironmentService } = require('../dist/services/environmentService');
        const envService = getEnvironmentService();
        await envService.initialize();
        this.addResult('Environment Service', true, 'Initialized successfully');
      } catch (error) {
        this.addResult('Environment Service', false, error.message);
      }
      
    } catch (error) {
      this.addResult('Environment Setup', false, error.message);
    }
  }

  async testDatabaseInitialization() {
    console.log('🗄️ Testing Database Initialization...');
    
    try {
      const { getDatabaseService } = require('../dist/services/databaseService');
      const dbService = getDatabaseService();
      
      await dbService.initialize();
      this.addResult('Database Initialization', true, 'Database initialized successfully');
      
      // Test basic database operations
      try {
        const testUser = {
          userId: 'test-user-' + Date.now(),
          pin: '123456',
          fullName: 'Test User',
          address: 'Test Address',
          phone: '1234567890',
          email: '<EMAIL>',
          subscriptionStatus: 'trial',
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        await dbService.createUser(testUser);
        const retrievedUser = await dbService.getUserByEmail('<EMAIL>');
        
        if (retrievedUser && retrievedUser.userId === testUser.userId) {
          this.addResult('Database Operations', true, 'CRUD operations working');
          
          // Cleanup test data
          await dbService.sqliteService.run('DELETE FROM users WHERE email = ?', ['<EMAIL>']);
        } else {
          this.addResult('Database Operations', false, 'Failed to retrieve test user');
        }
      } catch (error) {
        this.addResult('Database Operations', false, error.message);
      }
      
    } catch (error) {
      this.addResult('Database Initialization', false, error.message);
    }
  }

  async testFilePermissions() {
    console.log('🔐 Testing File Permissions...');
    
    const testPaths = [
      { name: 'User Data', path: this.userDataPath },
      { name: 'Database', path: path.join(this.userDataPath, 'zyka.db') },
      { name: 'Config', path: path.join(this.userDataPath, 'app-config.json') },
      { name: 'Backups', path: path.join(this.userDataPath, 'backups') },
      { name: 'Images', path: path.join(this.userDataPath, 'images') }
    ];
    
    for (const testPath of testPaths) {
      try {
        const dir = path.dirname(testPath.path);
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }
        
        // Test write access
        const testFile = path.join(dir, 'test-permission.tmp');
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
        
        this.addResult(`${testPath.name} Permissions`, true, 'Read/Write access confirmed');
      } catch (error) {
        this.addResult(`${testPath.name} Permissions`, false, error.message);
      }
    }
  }

  async testEmailConfiguration() {
    console.log('📧 Testing Email Configuration...');
    
    try {
      const { getEnvironmentService } = require('../dist/services/environmentService');
      const envService = getEnvironmentService();
      
      const emailConfig = envService.getEmailConfig();
      
      if (emailConfig.user && emailConfig.pass) {
        this.addResult('Email Config Available', true, 'Email credentials configured');
        
        // Test email service (without actually sending)
        const nodemailer = require('nodemailer');
        try {
          const transporter = nodemailer.createTransporter({
            service: emailConfig.service,
            auth: {
              user: emailConfig.user,
              pass: emailConfig.pass
            }
          });
          
          // Just verify configuration, don't send
          await transporter.verify();
          this.addResult('Email Service', true, 'Email service configuration valid');
        } catch (error) {
          this.addResult('Email Service', false, `Email verification failed: ${error.message}`);
        }
      } else {
        this.addResult('Email Config Available', false, 'Email credentials not configured (optional)');
      }
      
    } catch (error) {
      this.addResult('Email Configuration', false, error.message);
    }
  }

  async testAutoUpdater() {
    console.log('🔄 Testing Auto-Updater...');
    
    try {
      const { getAutoUpdaterService } = require('../dist/services/autoUpdaterService');
      const updaterService = getAutoUpdaterService();
      
      // Test updater configuration
      const updateServerUrl = 'https://zyka-pos-updates.pages.dev';
      
      // Test if update server is accessible
      const https = require('https');
      const testUrl = new Promise((resolve, reject) => {
        const req = https.get(updateServerUrl, (res) => {
          resolve(res.statusCode === 200);
        });
        req.on('error', reject);
        req.setTimeout(5000, () => {
          req.destroy();
          reject(new Error('Timeout'));
        });
      });
      
      try {
        const isAccessible = await testUrl;
        this.addResult('Update Server', isAccessible, updateServerUrl);
      } catch (error) {
        this.addResult('Update Server', false, `Cannot reach update server: ${error.message}`);
      }
      
      this.addResult('Auto-Updater Service', true, 'Auto-updater service available');
      
    } catch (error) {
      this.addResult('Auto-Updater', false, error.message);
    }
  }

  async testUserInterface() {
    console.log('🖥️ Testing User Interface...');
    
    try {
      // Test if we can create a window
      const testWindow = new BrowserWindow({
        width: 800,
        height: 600,
        show: false,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          preload: path.join(__dirname, '../dist/preload.js')
        }
      });
      
      this.addResult('Window Creation', true, 'Can create application windows');
      
      // Test if preload script exists
      const preloadPath = path.join(__dirname, '../dist/preload.js');
      const preloadExists = fs.existsSync(preloadPath);
      this.addResult('Preload Script', preloadExists, preloadPath);
      
      // Test if main renderer exists
      const rendererPath = path.join(__dirname, '../dist/renderer.js');
      const rendererExists = fs.existsSync(rendererPath);
      this.addResult('Renderer Script', rendererExists, rendererPath);
      
      testWindow.close();
      
    } catch (error) {
      this.addResult('User Interface', false, error.message);
    }
  }

  addResult(test, passed, details) {
    this.testResults.push({ test, passed, details });
    const status = passed ? '✅' : '❌';
    console.log(`  ${status} ${test}: ${details}`);
  }

  printResults() {
    console.log('\n📊 Installation Test Results:');
    console.log('=' * 50);
    
    const passed = this.testResults.filter(r => r.passed).length;
    const total = this.testResults.length;
    
    console.log(`\n✅ Passed: ${passed}/${total} tests`);
    
    if (passed === total) {
      console.log('\n🎉 All tests passed! Installation is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Check the details above.');
      
      const failed = this.testResults.filter(r => !r.passed);
      console.log('\nFailed tests:');
      failed.forEach(test => {
        console.log(`  ❌ ${test.test}: ${test.details}`);
      });
    }
    
    console.log('\n📋 Installation Summary:');
    console.log(`  User Data Path: ${this.userDataPath}`);
    console.log(`  Database Path: ${path.join(this.userDataPath, 'zyka.db')}`);
    console.log(`  Config Path: ${path.join(this.userDataPath, 'app-config.json')}`);
    console.log(`  App Version: ${app.getVersion()}`);
    console.log(`  Electron Version: ${process.versions.electron}`);
    console.log(`  Node Version: ${process.versions.node}`);
    console.log(`  Platform: ${process.platform}`);
    console.log(`  Architecture: ${process.arch}`);
  }
}

// Run tests when app is ready
app.whenReady().then(async () => {
  const tester = new InstallationTester();
  await tester.runAllTests();
  app.quit();
});

app.on('window-all-closed', () => {
  app.quit();
});
