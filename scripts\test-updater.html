<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Auto-Updater Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            min-height: 200px;
            overflow-y: auto;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #2196f3;
            background: #f8f9fa;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976d2;
        }
        .info {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Auto-Updater Test</h1>
        
        <div class="info">
            <strong>Instructions:</strong>
            <ul>
                <li>This test window will show auto-updater events and messages</li>
                <li>Check the console for detailed logs</li>
                <li>The updater will automatically check for updates after 3 seconds</li>
                <li>Use the buttons below to manually trigger updater actions</li>
            </ul>
        </div>

        <div>
            <button onclick="checkForUpdates()">Check for Updates</button>
            <button onclick="clearMessages()">Clear Messages</button>
            <button onclick="showConfig()">Show Config</button>
        </div>

        <h3>Update Status:</h3>
        <div id="status" class="status">
            <div class="message">Waiting for updater events...</div>
        </div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');
        const statusDiv = document.getElementById('status');

        function addMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            statusDiv.appendChild(messageDiv);
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }

        function clearMessages() {
            statusDiv.innerHTML = '<div class="message">Messages cleared...</div>';
        }

        function checkForUpdates() {
            addMessage('Manual update check requested...');
            // In a real implementation, you would call the updater service here
            // For this test, we'll just show a message
            setTimeout(() => {
                addMessage('Manual check completed (test mode)');
            }, 1000);
        }

        function showConfig() {
            const config = {
                platform: process.platform,
                arch: process.arch,
                version: process.env.npm_package_version || 'unknown',
                nodeEnv: process.env.NODE_ENV || 'development'
            };
            addMessage(`Configuration: ${JSON.stringify(config, null, 2)}`);
        }

        // Listen for updater messages
        ipcRenderer.on('updater-message', (event, message) => {
            addMessage(message);
        });

        // Initial message
        addMessage('Auto-updater test initialized');
        addMessage(`Platform: ${process.platform}, Arch: ${process.arch}`);
    </script>
</body>
</html>
