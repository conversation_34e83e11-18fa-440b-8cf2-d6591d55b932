const { app, BrowserWindow } = require('electron');
const { autoUpdater } = require('electron-updater');
const path = require('path');

// Enable debug logging
process.env.ELECTRON_UPDATER_DEBUG = 'true';

// Force development updates for testing
process.env.ELECTRON_IS_DEV = '0';

// Configure auto-updater for Cloudflare Pages
autoUpdater.setFeedURL({
  provider: 'generic',
  url: 'https://zyka-pos-updates.pages.dev'
});

// Force update check in development
autoUpdater.forceDevUpdateConfig = true;

let mainWindow;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  // Load a simple HTML page for testing
  mainWindow.loadFile(path.join(__dirname, 'test-updater.html'));

  // Open DevTools for debugging
  mainWindow.webContents.openDevTools();
}

// Auto-updater event handlers for testing
autoUpdater.on('checking-for-update', () => {
  console.log('Checking for update...');
  if (mainWindow) {
    mainWindow.webContents.send('updater-message', 'Checking for update...');
  }
});

autoUpdater.on('update-available', (info) => {
  console.log('Update available:', info);
  if (mainWindow) {
    mainWindow.webContents.send('updater-message', `Update available: ${info.version}`);
  }
});

autoUpdater.on('update-not-available', (info) => {
  console.log('Update not available:', info);
  if (mainWindow) {
    mainWindow.webContents.send('updater-message', 'Update not available');
  }
});

autoUpdater.on('error', (err) => {
  console.error('Update error:', err);
  if (mainWindow) {
    mainWindow.webContents.send('updater-message', `Update error: ${err.message}`);
  }
});

autoUpdater.on('download-progress', (progressObj) => {
  let log_message = "Download speed: " + progressObj.bytesPerSecond;
  log_message = log_message + ' - Downloaded ' + progressObj.percent + '%';
  log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')';
  console.log(log_message);
  if (mainWindow) {
    mainWindow.webContents.send('updater-message', log_message);
  }
});

autoUpdater.on('update-downloaded', (info) => {
  console.log('Update downloaded:', info);
  if (mainWindow) {
    mainWindow.webContents.send('updater-message', 'Update downloaded');
  }
});

app.whenReady().then(() => {
  createWindow();

  // Check for updates after window is ready
  setTimeout(() => {
    console.log('Starting update check...');
    autoUpdater.checkForUpdatesAndNotify();
  }, 3000);

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

// Test configuration
console.log('Auto-updater configuration:');
console.log('- App version:', app.getVersion());
console.log('- Platform:', process.platform);
console.log('- Arch:', process.arch);
console.log('- Environment:', process.env.NODE_ENV || 'development');
console.log('- Update server: https://zyka-pos-updates.pages.dev');
console.log('- Force dev updates:', autoUpdater.forceDevUpdateConfig);
console.log('- ELECTRON_IS_DEV:', process.env.ELECTRON_IS_DEV);
