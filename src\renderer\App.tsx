import React, { useState, useEffect } from 'react';
import UserDetailsStep from './pages/UserDetailsStep';
import RestaurantSetupStep from './pages/RestaurantSetupStep';
import PinLoginStep from './pages/PinLoginStep';
import Dashboard from './pages/Dashboard';
import { UserDetails, RestaurantDetails } from './types';
import { MenuProvider } from './contexts/MenuContext';
import { DashboardProvider } from './contexts/DashboardContext';
import { NotificationProvider } from './contexts/NotificationContext';

type AppStep = 'loading' | 'user-details' | 'restaurant-setup' | 'pin-login' | 'dashboard';

const App: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<AppStep>('loading');
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
  const [restaurantDetails, setRestaurantDetails] = useState<RestaurantDetails | null>(null);
  const [isSetupComplete, setIsSetupComplete] = useState(false);
  const [slideDirection, setSlideDirection] = useState<'left' | 'right'>('right');

  // Check for updates and setup status on app load
  useEffect(() => {
    const initializeApp = async () => {
      try {
        setCurrentStep('loading');

        // Check setup status without update check
        const setupStatus = await window.electronAPI.checkSetupStatus();
        setIsSetupComplete(setupStatus.isSetupComplete);

        if (setupStatus.isSetupComplete) {
          // Load existing user and restaurant data
          const userData = await window.electronAPI.getUserData();
          const restaurantData = await window.electronAPI.getRestaurantData();

          if (userData) {
            setUserDetails(userData);
          }
          if (restaurantData) {
            setRestaurantDetails(restaurantData);
          }

          // Go directly to PIN login for existing setup
          setCurrentStep('pin-login');
        } else {
          // Start onboarding flow
          setCurrentStep('user-details');
        }
      } catch (error) {
        console.error('Error during app initialization:', error);
        setCurrentStep('user-details');
      }
    };

    initializeApp();
  }, []);

  const handleUserDetailsComplete = (details: UserDetails) => {
    setUserDetails(details);
    setSlideDirection('right');
    setCurrentStep('restaurant-setup');
  };

  const handleRestaurantSetupBack = () => {
    setSlideDirection('left');
    setCurrentStep('user-details');
  };

  const handleRestaurantSetupComplete = (details: RestaurantDetails) => {
    setRestaurantDetails(details);
    setSlideDirection('right');
    setCurrentStep('pin-login');
  };

  const handlePinLoginComplete = async () => {
    try {
      console.log('PIN login complete, loading user data...');

      // Load user and restaurant data before going to dashboard
      const userData = await window.electronAPI.getUserData();
      const restaurantData = await window.electronAPI.getRestaurantData();

      console.log('Loaded user data:', userData);
      console.log('Loaded restaurant data:', restaurantData);

      if (userData) {
        setUserDetails(userData);
      }
      if (restaurantData) {
        setRestaurantDetails(restaurantData);
      }

      // Go to dashboard
      setCurrentStep('dashboard');

      // Check for updates in background (non-blocking)
      setTimeout(async () => {
        try {
          const updateResult = await window.electronAPI.updaterCheckForUpdates();
          console.log('Background update check result:', updateResult);

          // Wait a moment to get update status
          await new Promise(resolve => setTimeout(resolve, 2000));

          // Update checking is now handled by the notification system in the dashboard
        } catch (error) {
          console.error('Background update check failed:', error);
        }
      }, 1000); // Check for updates 1 second after login
    } catch (error) {
      console.error('Error during post-login process:', error);
      setCurrentStep('dashboard');
    }
  };

  const handleLogout = async () => {
    try {
      // For now, just go back to pin login (we'll add windowed mode later)
      setCurrentStep('pin-login');
    } catch (error) {
      console.error('Error during logout:', error);
      setCurrentStep('pin-login');
    }
  };



  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'loading':
        return (
          <div className="fullscreen-app">
            <div className="onboarding-layout">
              <div className="onboarding-container">
                <div className="loading-container">
                  <div className="loading-spinner"></div>
                  <h2 className="onboarding-title">Loading Zyka POS...</h2>
                  <p className="onboarding-subtitle">
                    Please wait while we initialize the application
                  </p>
                </div>
              </div>
            </div>
          </div>
        );
      case 'user-details':
        return (
          <div className="fullscreen-app">
            <div className="onboarding-layout">
              <div className={`slide-container ${slideDirection === 'left' ? 'slide-enter-left' : 'slide-enter-right'}`}>
                <UserDetailsStep onComplete={handleUserDetailsComplete} />
              </div>
            </div>
          </div>
        );
      case 'restaurant-setup':
        return (
          <div className="fullscreen-app">
            <div className="onboarding-layout">
              <div className={`slide-container ${slideDirection === 'left' ? 'slide-enter-left' : 'slide-enter-right'}`}>
                <RestaurantSetupStep
                  onComplete={handleRestaurantSetupComplete}
                  onBack={handleRestaurantSetupBack}
                  userDetails={userDetails!}
                />
              </div>
            </div>
          </div>
        );
      case 'pin-login':
        return (
          <div className="fullscreen-app">
            <div className="onboarding-layout">
              <div className={`slide-container ${slideDirection === 'left' ? 'slide-enter-left' : 'slide-enter-right'}`}>
                <PinLoginStep
                  onComplete={handlePinLoginComplete}
                  userDetails={userDetails!}
                  restaurantDetails={restaurantDetails || undefined}
                />
              </div>
            </div>
          </div>
        );
      case 'dashboard':
        console.log('Rendering dashboard with:', { userDetails, restaurantDetails });
        if (!userDetails || !restaurantDetails) {
          console.error('Missing user or restaurant details for dashboard');
          return (
            <div className="fullscreen-app">
              <div className="onboarding-layout">
                <div className="loading-container">
                  <div className="loading-spinner"></div>
                  <h2 className="onboarding-title">Loading Dashboard...</h2>
                  <p className="onboarding-subtitle">
                    Please wait while we load your data
                  </p>
                </div>
              </div>
            </div>
          );
        }
        return (
          <div className="fullscreen-app">
            <div className="pos-layout">
              <NotificationProvider userId={userDetails.userId || restaurantDetails.userId}>
                <DashboardProvider restaurantId={restaurantDetails.userId}>
                  <MenuProvider restaurantId={restaurantDetails.userId}>
                    <Dashboard
                      userDetails={userDetails}
                      restaurantDetails={restaurantDetails}
                      onLogout={handleLogout}
                    />
                  </MenuProvider>
                </DashboardProvider>
              </NotificationProvider>
            </div>
          </div>
        );
      default:
        return (
          <div className="fullscreen-app">
            <div className="onboarding-layout">
              <UserDetailsStep onComplete={handleUserDetailsComplete} />
            </div>
          </div>
        );
    }
  };

  return (
    <div className="app">
      {renderCurrentStep()}
    </div>
  );
};

export default App;
