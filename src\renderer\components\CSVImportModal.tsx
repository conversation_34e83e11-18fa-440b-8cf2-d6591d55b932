import React, { useState } from 'react';

interface CSVImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (items: any[]) => Promise<void>;
}

interface ParsedItem {
  name: string;
  price: number;
  description?: string;
  category?: string;
  image?: string;
  available: boolean;
  errors: string[];
}

const CSVImportModal: React.FC<CSVImportModalProps> = ({ isOpen, onClose, onImport }) => {
  const [csvContent, setCsvContent] = useState('');
  const [parsedItems, setParsedItems] = useState<ParsedItem[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  if (!isOpen) return null;

  const sampleCSV = `name,price,description,category,image,available
Margherita Pizza,299.99,Fresh tomatoes mozzarella basil,Pizza,,true
Caesar Salad,199.99,Romaine lettuce parmesan croutons,Salads,,true
Grilled Chicken,349.99,Herb-seasoned grilled chicken breast,Main Course,,true
Chocolate Cake,149.99,Rich chocolate cake with frosting,Desserts,,true
Cappuccino,99.99,Espresso with steamed milk foam,Beverages,,true`;

  const parseCSV = (content: string): ParsedItem[] => {
    const lines = content.trim().split('\n');
    if (lines.length < 2) {
      return [];
    }

    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
    const requiredHeaders = ['name', 'price'];
    const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));

    if (missingHeaders.length > 0) {
      alert(`Missing required columns: ${missingHeaders.join(', ')}`);
      return [];
    }

    const items: ParsedItem[] = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      const item: ParsedItem = {
        name: '',
        price: 0,
        description: '',
        category: '',
        image: '',
        available: true,
        errors: [],
      };

      headers.forEach((header, index) => {
        const value = values[index] || '';

        switch (header) {
          case 'name':
            if (!value) {
              item.errors.push('Name is required');
            } else {
              item.name = value;
            }
            break;
          case 'price':
            const price = parseFloat(value);
            if (isNaN(price) || price <= 0) {
              item.errors.push('Valid price is required');
            } else {
              item.price = price;
            }
            break;
          case 'description':
            item.description = value;
            break;
          case 'category':
            item.category = value;
            break;
          case 'image':
            item.image = value;
            break;
          case 'available':
            item.available = value.toLowerCase() !== 'false' && value !== '0';
            break;
        }
      });

      items.push(item);
    }

    return items;
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith('.csv')) {
      alert('Please select a CSV file');
      return;
    }

    const reader = new FileReader();
    reader.onload = e => {
      const content = e.target?.result as string;
      setCsvContent(content);
      const parsed = parseCSV(content);
      setParsedItems(parsed);
      setShowPreview(true);
    };
    reader.readAsText(file);
  };

  const handleTextareaChange = (content: string) => {
    setCsvContent(content);
    if (content.trim()) {
      const parsed = parseCSV(content);
      setParsedItems(parsed);
      setShowPreview(true);
    } else {
      setParsedItems([]);
      setShowPreview(false);
    }
  };

  const handleImport = async () => {
    const validItems = parsedItems.filter(item => item.errors.length === 0);

    if (validItems.length === 0) {
      alert('No valid items to import');
      return;
    }

    if (parsedItems.some(item => item.errors.length > 0)) {
      const proceed = confirm(
        `${parsedItems.length - validItems.length} items have errors and will be skipped. Continue with ${validItems.length} valid items?`
      );
      if (!proceed) return;
    }

    setIsProcessing(true);
    try {
      await onImport(validItems);
      onClose();
      setCsvContent('');
      setParsedItems([]);
      setShowPreview(false);
    } catch (error) {
      console.error('Import failed:', error);
      alert('Import failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const downloadSample = () => {
    const blob = new Blob([sampleCSV], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'menu-items-sample.csv';
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="modal-overlay">
      <div className="csv-import-modal">
        <div className="modal-header">
          <h2>Import Menu Items from CSV</h2>
          <button className="close-btn" onClick={onClose}>
            ×
          </button>
        </div>

        <div className="modal-content">
          <div className="import-instructions">
            <h3>Instructions</h3>
            <p>Upload a CSV file or paste CSV content with the following columns:</p>
            <ul>
              <li>
                <strong>name</strong> (required) - Menu item name
              </li>
              <li>
                <strong>price</strong> (required) - Price in rupees
              </li>
              <li>
                <strong>description</strong> (optional) - Item description
              </li>
              <li>
                <strong>category</strong> (optional) - Item category
              </li>
              <li>
                <strong>image</strong> (optional) - Image URL
              </li>
              <li>
                <strong>available</strong> (optional) - true/false (default: true)
              </li>
            </ul>
            <button className="btn btn-secondary" onClick={downloadSample}>
              📥 Download Sample CSV
            </button>
          </div>

          <div className="import-methods">
            <div className="method-section">
              <h4>Upload CSV File</h4>
              <input type="file" accept=".csv" onChange={handleFileUpload} className="file-input" />
            </div>

            <div className="method-divider">OR</div>

            <div className="method-section">
              <h4>Paste CSV Content</h4>
              <textarea
                className="csv-textarea"
                placeholder="Paste your CSV content here..."
                value={csvContent}
                onChange={e => handleTextareaChange(e.target.value)}
                rows={8}
              />
            </div>
          </div>

          {showPreview && (
            <div className="preview-section">
              <h3>Preview ({parsedItems.length} items)</h3>
              <div className="preview-stats">
                <span className="stat valid">
                  ✅ {parsedItems.filter(item => item.errors.length === 0).length} Valid
                </span>
                <span className="stat invalid">
                  ❌ {parsedItems.filter(item => item.errors.length > 0).length} Invalid
                </span>
              </div>

              <div className="preview-table">
                <table>
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Price</th>
                      <th>Category</th>
                      <th>Available</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {parsedItems.slice(0, 10).map((item, index) => (
                      <tr key={index} className={item.errors.length > 0 ? 'error-row' : ''}>
                        <td>{item.name || '—'}</td>
                        <td>₹{item.price || 0}</td>
                        <td>{item.category || '—'}</td>
                        <td>{item.available ? 'Yes' : 'No'}</td>
                        <td>
                          {item.errors.length === 0 ? (
                            <span className="status-valid">✅ Valid</span>
                          ) : (
                            <span className="status-invalid" title={item.errors.join(', ')}>
                              ❌ {item.errors.length} error(s)
                            </span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {parsedItems.length > 10 && (
                  <p className="preview-note">Showing first 10 items...</p>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="modal-actions">
          <button className="cancel-btn" onClick={onClose}>
            Cancel
          </button>
          <button
            className="process-btn"
            onClick={handleImport}
            disabled={
              !showPreview ||
              parsedItems.filter(item => item.errors.length === 0).length === 0 ||
              isProcessing
            }
          >
            {isProcessing
              ? 'Importing...'
              : `Import ${parsedItems.filter(item => item.errors.length === 0).length} Items`}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CSVImportModal;
