import React, { useState, useEffect } from 'react';
import { DiscountInfo } from '../types';
import Icon from './Icon';

interface DiscountInputProps {
  subtotal: number;
  currentDiscount?: DiscountInfo;
  onDiscountChange: (discount: DiscountInfo | null) => void;
  disabled?: boolean;
}

const DiscountInput: React.FC<DiscountInputProps> = ({
  subtotal,
  currentDiscount,
  onDiscountChange,
  disabled = false,
}) => {
  const [discountType, setDiscountType] = useState<'percentage' | 'fixed'>(
    currentDiscount?.type || 'percentage'
  );
  const [discountValue, setDiscountValue] = useState<string>(
    currentDiscount?.value?.toString() || ''
  );
  const [discountReason, setDiscountReason] = useState<string>(
    currentDiscount?.reason || ''
  );
  const [showDiscountInput, setShowDiscountInput] = useState<boolean>(
    !!currentDiscount
  );

  useEffect(() => {
    if (currentDiscount) {
      setDiscountType(currentDiscount.type);
      setDiscountValue(currentDiscount.value.toString());
      setDiscountReason(currentDiscount.reason || '');
      setShowDiscountInput(true);
    }
  }, [currentDiscount]);

  const calculateDiscountAmount = (value: number, type: 'percentage' | 'fixed'): number => {
    if (type === 'percentage') {
      return (subtotal * value) / 100;
    }
    return Math.min(value, subtotal); // Fixed amount can't exceed subtotal
  };

  const getDiscountAmount = (): number => {
    const value = parseFloat(discountValue) || 0;
    if (value <= 0) return 0;
    return calculateDiscountAmount(value, discountType);
  };

  const handleApplyDiscount = () => {
    const value = parseFloat(discountValue) || 0;
    
    if (value <= 0) {
      onDiscountChange(null);
      return;
    }

    // Validate percentage (0-100%)
    if (discountType === 'percentage' && value > 100) {
      alert('Percentage discount cannot exceed 100%');
      return;
    }

    // Validate fixed amount (cannot exceed subtotal)
    if (discountType === 'fixed' && value > subtotal) {
      alert('Fixed discount cannot exceed the subtotal amount');
      return;
    }

    const discountInfo: DiscountInfo = {
      type: discountType,
      value: value,
      reason: discountReason.trim() || undefined,
      appliedBy: 'staff', // Could be dynamic based on logged-in user
    };

    onDiscountChange(discountInfo);
  };

  const handleRemoveDiscount = () => {
    setDiscountValue('');
    setDiscountReason('');
    setShowDiscountInput(false);
    onDiscountChange(null);
  };

  const handleDiscountValueChange = (value: string) => {
    // Allow only numbers and decimal point
    const numericValue = value.replace(/[^0-9.]/g, '');
    setDiscountValue(numericValue);
  };

  const discountAmount = getDiscountAmount();
  const isValidDiscount = parseFloat(discountValue) > 0 && discountAmount > 0;

  return (
    <div className="discount-input-container">
      {!showDiscountInput ? (
        <button
          className="btn btn-secondary discount-toggle-btn"
          onClick={() => setShowDiscountInput(true)}
          disabled={disabled}
        >
          <Icon name="tag" size="xs" />
          Apply Discount
        </button>
      ) : (
        <div className="discount-input-form">
          <div className="discount-header">
            <h4>Apply Discount</h4>
            <button
              className="btn-icon danger"
              onClick={handleRemoveDiscount}
              disabled={disabled}
            >
              <Icon name="x-circle" size="xs" />
            </button>
          </div>

          <div className="discount-type-selector">
            <label className="radio-label">
              <input
                type="radio"
                name="discountType"
                value="percentage"
                checked={discountType === 'percentage'}
                onChange={(e) => setDiscountType(e.target.value as 'percentage')}
                disabled={disabled}
              />
              <span className="radio-custom"></span>
              Percentage (%)
            </label>
            <label className="radio-label">
              <input
                type="radio"
                name="discountType"
                value="fixed"
                checked={discountType === 'fixed'}
                onChange={(e) => setDiscountType(e.target.value as 'fixed')}
                disabled={disabled}
              />
              <span className="radio-custom"></span>
              Fixed Amount (₹)
            </label>
          </div>

          <div className="discount-value-input">
            <div className="input-group">
              <span className="input-prefix">
                {discountType === 'percentage' ? '%' : '₹'}
              </span>
              <input
                type="text"
                value={discountValue}
                onChange={(e) => handleDiscountValueChange(e.target.value)}
                placeholder={discountType === 'percentage' ? '0.00' : '0.00'}
                className="discount-value-field"
                disabled={disabled}
              />
            </div>
            {discountType === 'percentage' && (
              <small className="discount-hint">
                Max: 100% | Current: {discountValue ? `₹${discountAmount.toFixed(2)}` : '₹0.00'}
              </small>
            )}
            {discountType === 'fixed' && (
              <small className="discount-hint">
                Max: ₹{subtotal.toFixed(2)} | Current: ₹{discountAmount.toFixed(2)}
              </small>
            )}
          </div>

          <div className="discount-reason-input">
            <input
              type="text"
              value={discountReason}
              onChange={(e) => setDiscountReason(e.target.value)}
              placeholder="Reason for discount (optional)"
              className="discount-reason-field"
              disabled={disabled}
            />
          </div>

          <div className="discount-summary">
            <div className="discount-calculation">
              <div className="calc-row">
                <span>Subtotal:</span>
                <span>₹{subtotal.toFixed(2)}</span>
              </div>
              <div className="calc-row discount-row">
                <span>
                  Discount ({discountType === 'percentage' ? `${discountValue}%` : `₹${discountValue}`}):
                </span>
                <span className="discount-amount">-₹{discountAmount.toFixed(2)}</span>
              </div>
              <div className="calc-row total-row">
                <span>After Discount:</span>
                <span>₹{(subtotal - discountAmount).toFixed(2)}</span>
              </div>
            </div>
          </div>

          <div className="discount-actions">
            <button
              className="btn btn-secondary"
              onClick={handleRemoveDiscount}
              disabled={disabled}
            >
              Cancel
            </button>
            <button
              className="btn btn-primary"
              onClick={handleApplyDiscount}
              disabled={!isValidDiscount || disabled}
            >
              Apply Discount
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DiscountInput;
