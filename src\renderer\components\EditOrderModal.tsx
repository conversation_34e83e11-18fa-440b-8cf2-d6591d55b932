import React, { useState, useMemo } from 'react';
import { Order, OrderItem, MenuItem } from '../types';
import { useMenu } from '../contexts/MenuContext';
import { useNotifications } from '../contexts/NotificationContext';
import Icon from './Icon';

interface EditOrderModalProps {
  order: Order;
  onClose: () => void;
  onOrderUpdated: (updatedOrder: Order) => void;
  restaurantId: string;
}

const EditOrderModal: React.FC<EditOrderModalProps> = ({
  order,
  onClose,
  onOrderUpdated,
  restaurantId,
}) => {
  const { menuItems } = useMenu();
  const { showToast } = useNotifications();
  const [editedItems, setEditedItems] = useState<OrderItem[]>(order.items);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Get unique categories from menu items
  const categories = useMemo(() => {
    const cats = ['all', ...new Set(menuItems.map(item => item.category).filter(Boolean))] as string[];
    return cats;
  }, [menuItems]);

  // Filter menu items based on search and category
  const filteredMenuItems = useMemo(() => {
    return menuItems.filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (item.code && item.code.toLowerCase().includes(searchQuery.toLowerCase()));
      const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
      return matchesSearch && matchesCategory && item.available;
    }).sort((a, b) => {
      // Sort by popularity (most ordered first), then by name
      const popularityDiff = (b.popularity || 0) - (a.popularity || 0);
      if (popularityDiff !== 0) return popularityDiff;
      return a.name.localeCompare(b.name);
    });
  }, [menuItems, searchQuery, selectedCategory]);

  const calculateTotal = () => {
    return editedItems.reduce((total, item) => total + item.subtotal, 0);
  };

  // Add item to edited order
  const addToOrder = (item: MenuItem) => {
    setEditedItems(prevItems => {
      const existingItem = prevItems.find(orderItem => orderItem.menuItemId === item.id);

      if (existingItem) {
        return prevItems.map(orderItem =>
          orderItem.menuItemId === item.id
            ? {
                ...orderItem,
                quantity: orderItem.quantity + 1,
                subtotal: (orderItem.quantity + 1) * orderItem.menuItemPrice
              }
            : orderItem
        );
      } else {
        const newOrderItem: OrderItem = {
          id: `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          menuItemId: item.id,
          menuItemName: item.name,
          menuItemPrice: item.price,
          quantity: 1,
          subtotal: item.price,
          notes: '',
          status: 'pending',
        };
        return [...prevItems, newOrderItem];
      }
    });
  };

  // Update item quantity
  const updateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      setEditedItems(prevItems => prevItems.filter(item => item.id !== itemId));
    } else {
      setEditedItems(prevItems =>
        prevItems.map(item =>
          item.id === itemId
            ? {
                ...item,
                quantity: newQuantity,
                subtotal: newQuantity * item.menuItemPrice
              }
            : item
        )
      );
    }
  };

  // Handle save changes
  const handleSaveChanges = async () => {
    try {
      const updatedOrder: Order = {
        ...order,
        items: editedItems,
        subtotal: calculateTotal(),
        totalAmount: calculateTotal(),
      };

      const result = await window.electronAPI.updateOrder(order.id, {
        items: editedItems,
        subtotal: calculateTotal(),
        totalAmount: calculateTotal(),
      });

      if (result.success && result.order) {
        showToast({
          type: 'success',
          title: 'Order Updated',
          message: `Order ${order.orderNumber} has been updated successfully`
        });
        onOrderUpdated(result.order);
        onClose();
      } else {
        showToast({
          type: 'error',
          title: 'Update Failed',
          message: 'Failed to update order: ' + (result.error || 'Unknown error')
        });
      }
    } catch (error) {
      console.error('Error updating order:', error);
      showToast({
        type: 'error',
        title: 'Update Failed',
        message: 'Failed to update order'
      });
    }
  };

  // Handle cancel
  const handleCancel = () => {
    setEditedItems(order.items); // Reset to original items
    onClose();
  };

  return (
    <div className="menu-selection-modal-overlay">
      <div className="menu-selection-modal">
        {/* Modal Header */}
        <div className="modal-header-new">
          <div className="table-info-header">
            <Icon name="edit" size="md" />
            <div>
              <h3>Edit Order {order.orderNumber}</h3>
              {order.tableNumber && <p>Table {order.tableNumber}</p>}
            </div>
          </div>
          <button className="close-btn-new" onClick={handleCancel}>
            <Icon name="cancel" size="sm" />
          </button>
        </div>

        {/* Modal Content */}
        <div className="modal-content-new">
          {/* Left Side - Menu Items */}
          <div className="modal-left-panel">
            <div className="menu-controls-modal">
              <div className="search-section-modal">
                <input
                  type="text"
                  placeholder="Search menu items..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="search-input-modal"
                />
                <button className="search-btn-modal">
                  <Icon name="search" size="sm" />
                </button>
              </div>

              <div className="category-tabs-modal">
                {categories.map(category => (
                  <button
                    key={category}
                    className={`category-tab-modal ${selectedCategory === category ? 'active' : ''}`}
                    onClick={() => setSelectedCategory(category)}
                    onTouchEnd={(e) => {
                      e.preventDefault();
                      setSelectedCategory(category);
                    }}
                  >
                    {category === 'all' ? 'All' : (category || 'Uncategorized')}
                  </button>
                ))}
              </div>
            </div>

            <div className="menu-items-modal">
              {filteredMenuItems.map(item => (
                <div
                  key={item.id}
                  className="menu-item-modal"
                  onClick={() => addToOrder(item)}
                  onTouchEnd={(e) => {
                    e.preventDefault();
                    addToOrder(item);
                  }}
                >
                  <div className="item-info-modal">
                    <div className="item-name-modal">{item.name}</div>
                    <div className="item-price-modal">₹{item.price.toFixed(2)}</div>
                    {item.description && (
                      <div className="item-description-modal">{item.description}</div>
                    )}
                    {item.code && (
                      <div className="item-code-modal">Code: {item.code}</div>
                    )}
                  </div>
                  <div className="quantity-controls-modal">
                    <button
                      className="qty-btn-modal decrease"
                      onClick={(e) => {
                        e.stopPropagation();
                        const existingItem = editedItems.find(orderItem => orderItem.menuItemId === item.id);
                        if (existingItem) {
                          updateQuantity(existingItem.id, existingItem.quantity - 1);
                        }
                      }}
                      onTouchEnd={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        const existingItem = editedItems.find(orderItem => orderItem.menuItemId === item.id);
                        if (existingItem) {
                          updateQuantity(existingItem.id, existingItem.quantity - 1);
                        }
                      }}
                    >
                      -
                    </button>
                    <span className="quantity-display-modal">
                      {editedItems.find(orderItem => orderItem.menuItemId === item.id)?.quantity || 0}
                    </span>
                    <button
                      className="qty-btn-modal increase"
                      onClick={(e) => {
                        e.stopPropagation();
                        addToOrder(item);
                      }}
                      onTouchEnd={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        addToOrder(item);
                      }}
                    >
                      +
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Side - Current Order Items */}
          <div className="modal-right-panel">
            <div className="selected-items-section">
              <h4>Order Items</h4>

              {editedItems.length === 0 ? (
                <div className="empty-selection">
                  <Icon name="shopping-cart" size="lg" />
                  <p>No items in order</p>
                  <p className="empty-subtitle">Add items from the menu</p>
                </div>
              ) : (
                <div className="selected-items-list">
                  {editedItems.map((item, index) => (
                    <div key={item.id} className="selected-item-modal">
                      <div className="item-index-modal">{index + 1}</div>
                      <div className="item-details-modal">
                        <div className="item-name-modal">{item.menuItemName}</div>
                        <div className="item-price-modal">₹{item.menuItemPrice.toFixed(2)} each</div>
                      </div>
                      <div className="quantity-section-modal">
                        <div className="quantity-controls-modal">
                          <button
                            className="qty-btn-modal decrease"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            onTouchEnd={(e) => {
                              e.preventDefault();
                              updateQuantity(item.id, item.quantity - 1);
                            }}
                          >
                            -
                          </button>
                          <span className="quantity-display-modal">{item.quantity}</span>
                          <button
                            className="qty-btn-modal increase"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            onTouchEnd={(e) => {
                              e.preventDefault();
                              updateQuantity(item.id, item.quantity + 1);
                            }}
                          >
                            +
                          </button>
                        </div>
                        <div className="item-subtotal-modal">₹{item.subtotal.toFixed(2)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {editedItems.length > 0 && (
                <div className="selection-total">
                  <div className="total-items">{editedItems.length} items</div>
                  <div className="total-amount">Total: ₹{calculateTotal().toFixed(2)}</div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Modal Footer */}
        <div className="modal-footer-new">
          <button
            className="modal-btn-secondary"
            onClick={handleCancel}
            onTouchEnd={(e) => {
              e.preventDefault();
              handleCancel();
            }}
          >
            Cancel
          </button>
          <button
            className="modal-btn-primary"
            onClick={handleSaveChanges}
            onTouchEnd={(e) => {
              e.preventDefault();
              handleSaveChanges();
            }}
            disabled={editedItems.length === 0}
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditOrderModal;
