import React, { useState, useEffect } from 'react';
import Icon from './Icon';

interface ImageResolverProps {
  src?: string;
  alt: string;
  className?: string;
  fallback?: string;
}

const ImageResolver: React.FC<ImageResolverProps> = ({
  src,
  alt,
  className = '',
  fallback = '/placeholder-food.jpg',
}) => {
  const [resolvedSrc, setResolvedSrc] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const resolveImagePath = async () => {
      if (!src) {
        setResolvedSrc(null);
        setIsLoading(false);
        return;
      }

      // If it's already a full URL (http/https), use it directly
      if (src.startsWith('http://') || src.startsWith('https://')) {
        setResolvedSrc(src);
        setIsLoading(false);
        return;
      }

      // If it's a local path, resolve it through electron
      try {
        const result = await window.electronAPI.getImagePath(src);
        if (result.success && result.fullPath) {
          // Convert file path to file:// URL for electron
          const fileUrl = `file://${result.fullPath.replace(/\\/g, '/')}`;
          setResolvedSrc(fileUrl);
        } else {
          setResolvedSrc(null);
        }
      } catch (error) {
        console.error('Error resolving image path:', error);
        setResolvedSrc(null);
      } finally {
        setIsLoading(false);
      }
    };

    resolveImagePath();
  }, [src]);

  const handleError = () => {
    setHasError(true);
  };

  const handleLoad = () => {
    setHasError(false);
  };

  if (isLoading) {
    return (
      <div className={`image-placeholder loading ${className}`}>
        <div className="loading-spinner"></div>
      </div>
    );
  }

  if (!resolvedSrc || hasError) {
    return (
      <div className={`image-placeholder no-image ${className}`}>
        <span className="placeholder-icon">
          <Icon name="image" size="lg" />
        </span>
        <span className="placeholder-text">No Image</span>
      </div>
    );
  }

  return (
    <img
      src={resolvedSrc}
      alt={alt}
      className={className}
      onError={handleError}
      onLoad={handleLoad}
    />
  );
};

export default ImageResolver;
