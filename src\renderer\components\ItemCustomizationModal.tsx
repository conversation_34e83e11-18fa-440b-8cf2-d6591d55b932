import React, { useState, useEffect } from 'react';
import { MenuItem, ModifierGroup, Modifier, SelectedModifier } from '../types';
import Icon from './Icon';

interface ItemCustomizationModalProps {
  menuItem: MenuItem;
  onConfirm: (item: MenuItem, modifiers: SelectedModifier[], notes: string, quantity: number) => void;
  onClose: () => void;
  initialQuantity?: number;
}

const ItemCustomizationModal: React.FC<ItemCustomizationModalProps> = ({
  menuItem,
  onConfirm,
  onClose,
  initialQuantity = 1,
}) => {
  const [modifierGroups, setModifierGroups] = useState<ModifierGroup[]>([]);
  const [selectedModifiers, setSelectedModifiers] = useState<{ [groupId: string]: SelectedModifier[] }>({});
  const [notes, setNotes] = useState('');
  const [quantity, setQuantity] = useState(initialQuantity);
  const [isLoading, setIsLoading] = useState(true);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  useEffect(() => {
    loadModifierGroups();
  }, [menuItem.id]);

  const loadModifierGroups = async () => {
    try {
      setIsLoading(true);
      const groups = await window.electronAPI.getModifierGroupsForMenuItem(menuItem.id);
      setModifierGroups(groups);
      
      // Initialize selected modifiers object
      const initialSelected: { [groupId: string]: SelectedModifier[] } = {};
      groups.forEach(group => {
        initialSelected[group.id] = [];
      });
      setSelectedModifiers(initialSelected);
    } catch (error) {
      console.error('Failed to load modifier groups:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleModifierToggle = (groupId: string, modifier: Modifier) => {
    const group = modifierGroups.find(g => g.id === groupId);
    if (!group) return;

    const currentSelected = selectedModifiers[groupId] || [];
    const isSelected = currentSelected.some(m => m.id === modifier.id);

    let newSelected: SelectedModifier[];

    if (isSelected) {
      // Remove modifier
      newSelected = currentSelected.filter(m => m.id !== modifier.id);
    } else {
      // Add modifier
      const selectedModifier: SelectedModifier = {
        id: modifier.id,
        name: modifier.name,
        price: modifier.price,
      };

      if (group.maxSelection === 1) {
        // Single selection - replace existing
        newSelected = [selectedModifier];
      } else {
        // Multiple selection - check max limit
        if (currentSelected.length < group.maxSelection) {
          newSelected = [...currentSelected, selectedModifier];
        } else {
          return; // Max selection reached
        }
      }
    }

    setSelectedModifiers(prev => ({
      ...prev,
      [groupId]: newSelected,
    }));
  };

  const validateSelections = (): string[] => {
    const errors: string[] = [];

    modifierGroups.forEach(group => {
      const selected = selectedModifiers[group.id] || [];
      
      if (group.isRequired && selected.length < group.minSelection) {
        errors.push(`Please select at least ${group.minSelection} option(s) from "${group.name}"`);
      }
      
      if (selected.length > group.maxSelection) {
        errors.push(`You can select at most ${group.maxSelection} option(s) from "${group.name}"`);
      }
    });

    return errors;
  };

  const calculateTotalPrice = (): number => {
    let basePrice = menuItem.price;
    let modifierPrice = 0;

    Object.values(selectedModifiers).forEach(modifiers => {
      modifiers.forEach(modifier => {
        modifierPrice += modifier.price;
      });
    });

    return (basePrice + modifierPrice) * quantity;
  };

  const handleConfirm = () => {
    const errors = validateSelections();
    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }

    // Flatten selected modifiers
    const allSelectedModifiers: SelectedModifier[] = [];
    Object.values(selectedModifiers).forEach(modifiers => {
      allSelectedModifiers.push(...modifiers);
    });

    onConfirm(menuItem, allSelectedModifiers, notes, quantity);
  };

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1) {
      setQuantity(newQuantity);
    }
  };

  if (isLoading) {
    return (
      <div className="modal-overlay">
        <div className="modal-content">
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>Loading customization options...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="modal-overlay">
      <div className="modal-content item-customization-modal">
        <div className="modal-header">
          <div className="item-info">
            <h3>{menuItem.name}</h3>
            <p className="base-price">Base Price: ₹{menuItem.price.toFixed(2)}</p>
          </div>
          <button className="close-btn" onClick={onClose}>
            <Icon name="cancel" size="sm" />
          </button>
        </div>

        <div className="modal-body">
          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <div className="validation-errors">
              <Icon name="alert-triangle" size="sm" />
              <div className="error-list">
                {validationErrors.map((error, index) => (
                  <p key={index} className="error-message">{error}</p>
                ))}
              </div>
            </div>
          )}

          {/* Modifier Groups */}
          {modifierGroups.length > 0 ? (
            <div className="modifier-groups">
              {modifierGroups.map(group => (
                <div key={group.id} className="modifier-group">
                  <div className="group-header">
                    <h4>{group.name}</h4>
                    <div className="group-meta">
                      {group.isRequired && <span className="required-badge">Required</span>}
                      <span className="selection-info">
                        {group.minSelection === group.maxSelection 
                          ? `Select ${group.maxSelection}`
                          : `Select ${group.minSelection}-${group.maxSelection}`
                        }
                      </span>
                    </div>
                  </div>

                  {group.description && (
                    <p className="group-description">{group.description}</p>
                  )}

                  <div className="modifiers-list">
                    {group.modifiers?.map(modifier => {
                      const isSelected = selectedModifiers[group.id]?.some(m => m.id === modifier.id) || false;
                      const canSelect = !isSelected && (selectedModifiers[group.id]?.length || 0) < group.maxSelection;
                      const canDeselect = isSelected;

                      return (
                        <div
                          key={modifier.id}
                          className={`modifier-option ${isSelected ? 'selected' : ''} ${!canSelect && !canDeselect ? 'disabled' : ''}`}
                          onClick={() => (canSelect || canDeselect) && handleModifierToggle(group.id, modifier)}
                        >
                          <div className="modifier-info">
                            <div className="modifier-name">{modifier.name}</div>
                            {modifier.description && (
                              <div className="modifier-description">{modifier.description}</div>
                            )}
                          </div>
                          
                          <div className="modifier-price">
                            {modifier.price > 0 ? `+₹${modifier.price.toFixed(2)}` : 'Free'}
                          </div>
                          
                          <div className="modifier-checkbox">
                            {group.maxSelection === 1 ? (
                              <div className={`radio ${isSelected ? 'checked' : ''}`}></div>
                            ) : (
                              <div className={`checkbox ${isSelected ? 'checked' : ''}`}>
                                {isSelected && <Icon name="check" size="xs" />}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-modifiers">
              <Icon name="info" size="md" />
              <p>No customization options available for this item</p>
            </div>
          )}

          {/* Special Instructions */}
          <div className="special-instructions">
            <label htmlFor="notes">Special Instructions (Optional)</label>
            <textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Any special requests or notes for this item..."
              rows={3}
            />
          </div>
        </div>

        <div className="modal-footer">
          <div className="quantity-controls">
            <label>Quantity:</label>
            <div className="quantity-input">
              <button
                className="qty-btn decrease"
                onClick={() => handleQuantityChange(quantity - 1)}
                disabled={quantity <= 1}
              >
                <Icon name="minus" size="xs" />
              </button>
              <span className="quantity-display">{quantity}</span>
              <button
                className="qty-btn increase"
                onClick={() => handleQuantityChange(quantity + 1)}
              >
                <Icon name="plus" size="xs" />
              </button>
            </div>
          </div>

          <div className="total-price">
            <span className="total-label">Total:</span>
            <span className="total-amount">₹{calculateTotalPrice().toFixed(2)}</span>
          </div>

          <div className="action-buttons">
            <button className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button className="btn btn-primary" onClick={handleConfirm}>
              Add to Order
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ItemCustomizationModal;
