import React, { useState, useEffect } from 'react';
import { MenuItem, ModifierGroup } from '../types';
import Icon from './Icon';

interface MenuItemModifierAssignmentProps {
  menuItem: MenuItem;
  onClose: () => void;
  onUpdate: () => void;
}

const MenuItemModifierAssignment: React.FC<MenuItemModifierAssignmentProps> = ({
  menuItem,
  onClose,
  onUpdate,
}) => {
  const [allModifierGroups, setAllModifierGroups] = useState<ModifierGroup[]>([]);
  const [assignedGroups, setAssignedGroups] = useState<ModifierGroup[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, [menuItem.id]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      
      // Load all modifier groups for the restaurant
      const allGroups = await window.electronAPI.getModifierGroups(menuItem.restaurantId);
      setAllModifierGroups(allGroups);
      
      // Load assigned modifier groups for this menu item
      const assigned = await window.electronAPI.getModifierGroupsForMenuItem(menuItem.id);
      setAssignedGroups(assigned);
    } catch (error) {
      console.error('Failed to load modifier groups:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const isGroupAssigned = (groupId: string): boolean => {
    return assignedGroups.some(group => group.id === groupId);
  };

  const handleAssignGroup = async (group: ModifierGroup) => {
    try {
      const result = await window.electronAPI.assignModifierGroupToMenuItem(
        menuItem.id,
        group.id,
        group.isRequired,
        group.displayOrder
      );

      if (result.success) {
        await loadData();
        onUpdate();
      } else {
        alert('Failed to assign modifier group: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error assigning modifier group:', error);
      alert('Failed to assign modifier group');
    }
  };

  const handleRemoveGroup = async (group: ModifierGroup) => {
    try {
      const result = await window.electronAPI.removeModifierGroupFromMenuItem(
        menuItem.id,
        group.id
      );

      if (result.success) {
        await loadData();
        onUpdate();
      } else {
        alert('Failed to remove modifier group: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error removing modifier group:', error);
      alert('Failed to remove modifier group');
    }
  };

  if (isLoading) {
    return (
      <div className="modal-overlay">
        <div className="modal-content">
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>Loading modifier groups...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="modal-overlay">
      <div className="modal-content modifier-assignment-modal">
        <div className="modal-header">
          <div className="item-info">
            <h3>Assign Modifiers to "{menuItem.name}"</h3>
            <p>Select which modifier groups should be available for this menu item</p>
          </div>
          <button className="close-btn" onClick={onClose}>
            <Icon name="cancel" size="sm" />
          </button>
        </div>

        <div className="modal-body">
          {allModifierGroups.length === 0 ? (
            <div className="empty-state">
              <Icon name="tag" size="lg" />
              <p>No modifier groups available</p>
              <p className="empty-subtitle">Create modifier groups first to assign them to menu items</p>
            </div>
          ) : (
            <div className="modifier-groups-assignment">
              <div className="section">
                <h4>Available Modifier Groups</h4>
                <div className="groups-list">
                  {allModifierGroups.map(group => {
                    const isAssigned = isGroupAssigned(group.id);
                    
                    return (
                      <div
                        key={group.id}
                        className={`group-card ${isAssigned ? 'assigned' : 'available'}`}
                      >
                        <div className="group-info">
                          <div className="group-header">
                            <h5>{group.name}</h5>
                            <div className="group-badges">
                              {group.isRequired && (
                                <span className="badge required">Required</span>
                              )}
                              <span className="badge selection">
                                {group.minSelection}-{group.maxSelection} selections
                              </span>
                            </div>
                          </div>
                          
                          {group.description && (
                            <p className="group-description">{group.description}</p>
                          )}
                          
                          <div className="group-meta">
                            <span className="modifier-count">
                              {group.modifiers?.length || 0} modifiers
                            </span>
                          </div>
                        </div>

                        <div className="group-actions">
                          {isAssigned ? (
                            <button
                              className="btn btn-danger btn-sm"
                              onClick={() => handleRemoveGroup(group)}
                            >
                              <Icon name="minus" size="xs" />
                              Remove
                            </button>
                          ) : (
                            <button
                              className="btn btn-primary btn-sm"
                              onClick={() => handleAssignGroup(group)}
                            >
                              <Icon name="plus" size="xs" />
                              Assign
                            </button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {assignedGroups.length > 0 && (
                <div className="section">
                  <h4>Currently Assigned Groups ({assignedGroups.length})</h4>
                  <div className="assigned-groups-summary">
                    {assignedGroups.map(group => (
                      <div key={group.id} className="assigned-group-summary">
                        <div className="group-name">{group.name}</div>
                        <div className="group-details">
                          {group.isRequired && <span className="required-indicator">Required</span>}
                          <span className="modifier-count">
                            {group.modifiers?.length || 0} modifiers
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="modal-footer">
          <div className="assignment-summary">
            <span className="summary-text">
              {assignedGroups.length} modifier group(s) assigned to this item
            </span>
          </div>
          
          <div className="action-buttons">
            <button className="btn btn-primary" onClick={onClose}>
              Done
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MenuItemModifierAssignment;
