import React from 'react';
import { MenuItem, Table, OrderItem } from '../types';
import Icon from './Icon';

interface MenuSelectionModalProps {
  table: Table;
  menuItems: MenuItem[];
  categories: string[];
  currentOrder: OrderItem[];
  onAddToOrder: (item: MenuItem) => void;
  onUpdateQuantity: (itemId: string, quantity: number) => void;
  onClose: () => void;
  onConfirm: () => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  selectedCategory: string;
  setSelectedCategory: (category: string) => void;
}

const MenuSelectionModal: React.FC<MenuSelectionModalProps> = ({
  table,
  menuItems,
  categories,
  currentOrder,
  onAddToOrder,
  onUpdateQuantity,
  onClose,
  onConfirm,
  searchQuery,
  setSearchQuery,
  selectedCategory,
  setSelectedCategory,
}) => {
  // Get selected items for the right panel
  const selectedItems = currentOrder.filter(item => item.quantity > 0);

  return (
    <div className="menu-selection-modal-overlay">
      <div className="menu-selection-modal">
        {/* Modal Header */}
        <div className="modal-header-new">
          <div className="table-info-header">
            <Icon name="utensils" size="md" />
            <div>
              <h3>Table {table.tableNumber}</h3>
              <p>{table.capacity} pax • {table.location || 'Main Area'}</p>
            </div>
          </div>
          <button className="close-btn-new" onClick={onClose}>
            <Icon name="cancel" size="sm" />
          </button>
        </div>

        {/* Modal Content */}
        <div className="modal-content-new">
          {/* Left Side - Menu Items */}
          <div className="modal-left-panel">
            <div className="menu-controls-modal">
              <div className="search-section-modal">
                <input
                  type="text"
                  placeholder="Search menu items..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="search-input-modal"
                />
                <button className="search-btn-modal">
                  <Icon name="search" size="sm" />
                </button>
              </div>
              
              <div className="category-tabs-modal">
                {categories.map(category => (
                  <button
                    key={category}
                    className={`category-tab-modal ${selectedCategory === category ? 'active' : ''}`}
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category === 'all' ? 'All' : category}
                  </button>
                ))}
              </div>
            </div>

            <div className="menu-items-modal">
              {menuItems.map(item => (
                <div
                  key={item.id}
                  className="menu-item-modal"
                  onClick={() => onAddToOrder(item)}
                  onTouchEnd={(e) => {
                    e.preventDefault();
                    onAddToOrder(item);
                  }}
                >
                  <div className="item-info-modal">
                    <div className="item-name-modal">{item.name}</div>
                    <div className="item-price-modal">₹{item.price.toFixed(2)}</div>
                    {item.description && (
                      <div className="item-description-modal">{item.description}</div>
                    )}
                    {item.code && (
                      <div className="item-code-modal">Code: {item.code}</div>
                    )}
                  </div>
                  <div className="quantity-controls-modal">
                    <button
                      className="qty-btn-modal decrease"
                      onClick={(e) => {
                        e.stopPropagation();
                        const existingItem = currentOrder.find(orderItem => orderItem.menuItemId === item.id);
                        if (existingItem) {
                          onUpdateQuantity(existingItem.id, existingItem.quantity - 1);
                        }
                      }}
                      onTouchEnd={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        const existingItem = currentOrder.find(orderItem => orderItem.menuItemId === item.id);
                        if (existingItem) {
                          onUpdateQuantity(existingItem.id, existingItem.quantity - 1);
                        }
                      }}
                    >
                      -
                    </button>
                    <span className="quantity-display-modal">
                      {currentOrder.find(orderItem => orderItem.menuItemId === item.id)?.quantity || 0}
                    </span>
                    <button
                      className="qty-btn-modal increase"
                      onClick={(e) => {
                        e.stopPropagation();
                        onAddToOrder(item);
                      }}
                      onTouchEnd={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        onAddToOrder(item);
                      }}
                    >
                      +
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Side - Selected Items */}
          <div className="modal-right-panel">
            <div className="selected-items-section">
              <h4>Selected Items</h4>
              
              {selectedItems.length === 0 ? (
                <div className="empty-selection">
                  <Icon name="shopping-cart" size="lg" />
                  <p>No items selected</p>
                  <p className="empty-subtitle">Add items from the menu</p>
                </div>
              ) : (
                <div className="selected-items-list">
                  {selectedItems.map((item, index) => (
                    <div key={item.id} className="selected-item-modal">
                      <div className="item-index-modal">{index + 1}</div>
                      <div className="item-details-modal">
                        <span className="item-name-modal">{item.menuItemName}</span>
                        <span className="item-price-modal">₹{item.menuItemPrice} each</span>
                      </div>
                      <div className="quantity-section-modal">
                        <div className="quantity-controls-modal">
                          <button
                            className="qty-btn-modal decrease"
                            onClick={() => onUpdateQuantity(item.id, item.quantity - 1)}
                          >
                            -
                          </button>
                          <span className="quantity-display-modal">{item.quantity}</span>
                          <button
                            className="qty-btn-modal increase"
                            onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                          >
                            +
                          </button>
                        </div>
                        <div className="item-subtotal-modal">₹{item.subtotal.toFixed(2)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {selectedItems.length > 0 && (
                <div className="selection-total">
                  <div className="total-items">
                    {selectedItems.reduce((sum, item) => sum + item.quantity, 0)} items
                  </div>
                  <div className="total-amount">
                    Total: ₹{selectedItems.reduce((sum, item) => sum + item.subtotal, 0).toFixed(2)}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Modal Footer */}
        <div className="modal-footer-new">
          <button className="modal-btn-secondary" onClick={onClose}>
            Close
          </button>
          <button 
            className="modal-btn-primary" 
            onClick={onConfirm}
            disabled={selectedItems.length === 0}
          >
            Confirm Selection
          </button>
        </div>
      </div>
    </div>
  );
};

export default MenuSelectionModal;
