import React, { useState, useEffect } from 'react';
import { ModifierGroup, Modifier } from '../types';
import Icon from './Icon';

interface ModifierManagementProps {
  restaurantId: string;
}

interface ModifierGroupForm {
  name: string;
  description: string;
  isRequired: boolean;
  minSelection: number;
  maxSelection: number;
  displayOrder: number;
}

interface ModifierForm {
  name: string;
  price: string;
  description: string;
  displayOrder: number;
}

const ModifierManagement: React.FC<ModifierManagementProps> = ({ restaurantId }) => {
  const [modifierGroups, setModifierGroups] = useState<ModifierGroup[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<ModifierGroup | null>(null);
  const [showGroupForm, setShowGroupForm] = useState(false);
  const [showModifierForm, setShowModifierForm] = useState(false);
  const [editingGroup, setEditingGroup] = useState<ModifierGroup | null>(null);
  const [editingModifier, setEditingModifier] = useState<Modifier | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const [groupForm, setGroupForm] = useState<ModifierGroupForm>({
    name: '',
    description: '',
    isRequired: false,
    minSelection: 0,
    maxSelection: 1,
    displayOrder: 0,
  });

  const [modifierForm, setModifierForm] = useState<ModifierForm>({
    name: '',
    price: '0',
    description: '',
    displayOrder: 0,
  });

  useEffect(() => {
    loadModifierGroups();
  }, [restaurantId]);

  const loadModifierGroups = async () => {
    try {
      setIsLoading(true);
      const groups = await window.electronAPI.getModifierGroups(restaurantId);
      setModifierGroups(groups);
    } catch (error) {
      console.error('Failed to load modifier groups:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetGroupForm = () => {
    setGroupForm({
      name: '',
      description: '',
      isRequired: false,
      minSelection: 0,
      maxSelection: 1,
      displayOrder: 0,
    });
    setEditingGroup(null);
    setShowGroupForm(false);
  };

  const resetModifierForm = () => {
    setModifierForm({
      name: '',
      price: '0',
      description: '',
      displayOrder: 0,
    });
    setEditingModifier(null);
    setShowModifierForm(false);
  };

  const handleGroupSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const groupData = {
        ...groupForm,
        restaurantId,
        isActive: true,
      };

      let result;
      if (editingGroup) {
        result = await window.electronAPI.updateModifierGroup(editingGroup.id, groupData);
      } else {
        result = await window.electronAPI.createModifierGroup(groupData);
      }

      if (result.success) {
        await loadModifierGroups();
        resetGroupForm();
      } else {
        alert('Failed to save modifier group: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error saving modifier group:', error);
      alert('Failed to save modifier group');
    }
  };

  const handleModifierSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedGroup) {
      alert('Please select a modifier group first');
      return;
    }

    const price = parseFloat(modifierForm.price);
    if (isNaN(price) || price < 0) {
      alert('Please enter a valid price');
      return;
    }

    try {
      const modifierData = {
        ...modifierForm,
        price,
        modifierGroupId: selectedGroup.id,
        isAvailable: true,
      };

      let result;
      if (editingModifier) {
        result = await window.electronAPI.updateModifier(editingModifier.id, modifierData);
      } else {
        result = await window.electronAPI.createModifier(modifierData);
      }

      if (result.success) {
        await loadModifierGroups();
        resetModifierForm();
      } else {
        alert('Failed to save modifier: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error saving modifier:', error);
      alert('Failed to save modifier');
    }
  };

  const handleEditGroup = (group: ModifierGroup) => {
    setGroupForm({
      name: group.name,
      description: group.description || '',
      isRequired: group.isRequired,
      minSelection: group.minSelection,
      maxSelection: group.maxSelection,
      displayOrder: group.displayOrder,
    });
    setEditingGroup(group);
    setShowGroupForm(true);
  };

  const handleEditModifier = (modifier: Modifier) => {
    setModifierForm({
      name: modifier.name,
      price: modifier.price.toString(),
      description: modifier.description || '',
      displayOrder: modifier.displayOrder,
    });
    setEditingModifier(modifier);
    setShowModifierForm(true);
  };

  const handleDeleteGroup = async (group: ModifierGroup) => {
    if (!confirm(`Are you sure you want to delete the modifier group "${group.name}"?`)) {
      return;
    }

    try {
      const result = await window.electronAPI.deleteModifierGroup(group.id);
      if (result.success) {
        await loadModifierGroups();
        if (selectedGroup?.id === group.id) {
          setSelectedGroup(null);
        }
      } else {
        alert('Failed to delete modifier group: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error deleting modifier group:', error);
      alert('Failed to delete modifier group');
    }
  };

  const handleDeleteModifier = async (modifier: Modifier) => {
    if (!confirm(`Are you sure you want to delete the modifier "${modifier.name}"?`)) {
      return;
    }

    try {
      const result = await window.electronAPI.deleteModifier(modifier.id);
      if (result.success) {
        await loadModifierGroups();
      } else {
        alert('Failed to delete modifier: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error deleting modifier:', error);
      alert('Failed to delete modifier');
    }
  };

  if (isLoading) {
    return (
      <div className="modifier-management-loading">
        <div className="loading-spinner"></div>
        <p>Loading modifiers...</p>
      </div>
    );
  }

  return (
    <div className="modifier-management">
      <div className="modifier-management-header">
        <h2>Modifier Management</h2>
        <button 
          className="btn btn-primary" 
          onClick={() => setShowGroupForm(true)}
        >
          <Icon name="plus" size="xs" /> Add Modifier Group
        </button>
      </div>

      <div className="modifier-management-content">
        {/* Left Panel - Modifier Groups */}
        <div className="modifier-groups-panel">
          <h3>Modifier Groups</h3>
          
          {modifierGroups.length === 0 ? (
            <div className="empty-state">
              <Icon name="tag" size="lg" />
              <p>No modifier groups found</p>
              <p className="empty-subtitle">Create your first modifier group to get started</p>
            </div>
          ) : (
            <div className="modifier-groups-list">
              {modifierGroups.map(group => (
                <div
                  key={group.id}
                  className={`modifier-group-card ${selectedGroup?.id === group.id ? 'selected' : ''}`}
                  onClick={() => setSelectedGroup(group)}
                >
                  <div className="group-header">
                    <h4>{group.name}</h4>
                    <div className="group-actions">
                      <button
                        className="btn-icon"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditGroup(group);
                        }}
                      >
                        <Icon name="edit" size="xs" />
                      </button>
                      <button
                        className="btn-icon danger"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteGroup(group);
                        }}
                      >
                        <Icon name="trash" size="xs" />
                      </button>
                    </div>
                  </div>
                  
                  {group.description && (
                    <p className="group-description">{group.description}</p>
                  )}
                  
                  <div className="group-meta">
                    <span className={`group-badge ${group.isRequired ? 'required' : 'optional'}`}>
                      {group.isRequired ? 'Required' : 'Optional'}
                    </span>
                    <span className="group-selection">
                      {group.minSelection}-{group.maxSelection} selections
                    </span>
                    <span className="modifier-count">
                      {group.modifiers?.length || 0} modifiers
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Right Panel - Modifiers */}
        <div className="modifiers-panel">
          {selectedGroup ? (
            <>
              <div className="modifiers-header">
                <h3>Modifiers for "{selectedGroup.name}"</h3>
                <button 
                  className="btn btn-secondary" 
                  onClick={() => setShowModifierForm(true)}
                >
                  <Icon name="plus" size="xs" /> Add Modifier
                </button>
              </div>

              {selectedGroup.modifiers && selectedGroup.modifiers.length > 0 ? (
                <div className="modifiers-list">
                  {selectedGroup.modifiers.map(modifier => (
                    <div key={modifier.id} className="modifier-card">
                      <div className="modifier-info">
                        <h4>{modifier.name}</h4>
                        {modifier.description && (
                          <p className="modifier-description">{modifier.description}</p>
                        )}
                        <div className="modifier-price">₹{modifier.price.toFixed(2)}</div>
                      </div>
                      
                      <div className="modifier-actions">
                        <button
                          className="btn-icon"
                          onClick={() => handleEditModifier(modifier)}
                        >
                          <Icon name="edit" size="xs" />
                        </button>
                        <button
                          className="btn-icon danger"
                          onClick={() => handleDeleteModifier(modifier)}
                        >
                          <Icon name="trash" size="xs" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-state">
                  <Icon name="utensils" size="lg" />
                  <p>No modifiers in this group</p>
                  <p className="empty-subtitle">Add modifiers to customize menu items</p>
                </div>
              )}
            </>
          ) : (
            <div className="empty-state">
              <Icon name="arrow-left" size="lg" />
              <p>Select a modifier group</p>
              <p className="empty-subtitle">Choose a group from the left to view its modifiers</p>
            </div>
          )}
        </div>
      </div>

      {/* Modifier Group Form Modal */}
      {showGroupForm && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>{editingGroup ? 'Edit Modifier Group' : 'Add Modifier Group'}</h3>
              <button className="close-btn" onClick={resetGroupForm}>
                <Icon name="cancel" size="sm" />
              </button>
            </div>

            <form onSubmit={handleGroupSubmit} className="modifier-form">
              <div className="form-group">
                <label htmlFor="groupName">Group Name *</label>
                <input
                  id="groupName"
                  type="text"
                  value={groupForm.name}
                  onChange={(e) => setGroupForm({ ...groupForm, name: e.target.value })}
                  placeholder="e.g., Cheese Options, Size Options"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="groupDescription">Description</label>
                <textarea
                  id="groupDescription"
                  value={groupForm.description}
                  onChange={(e) => setGroupForm({ ...groupForm, description: e.target.value })}
                  placeholder="Optional description for this modifier group"
                  rows={3}
                />
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="minSelection">Minimum Selection</label>
                  <input
                    id="minSelection"
                    type="number"
                    min="0"
                    value={groupForm.minSelection}
                    onChange={(e) => setGroupForm({ ...groupForm, minSelection: parseInt(e.target.value) || 0 })}
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="maxSelection">Maximum Selection</label>
                  <input
                    id="maxSelection"
                    type="number"
                    min="1"
                    value={groupForm.maxSelection}
                    onChange={(e) => setGroupForm({ ...groupForm, maxSelection: parseInt(e.target.value) || 1 })}
                  />
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="displayOrder">Display Order</label>
                <input
                  id="displayOrder"
                  type="number"
                  min="0"
                  value={groupForm.displayOrder}
                  onChange={(e) => setGroupForm({ ...groupForm, displayOrder: parseInt(e.target.value) || 0 })}
                  placeholder="Order in which this group appears"
                />
              </div>

              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={groupForm.isRequired}
                    onChange={(e) => setGroupForm({ ...groupForm, isRequired: e.target.checked })}
                  />
                  <span className="checkmark"></span>
                  Required Selection
                </label>
                <small>Customers must select at least one modifier from this group</small>
              </div>

              <div className="form-actions">
                <button type="button" className="btn btn-secondary" onClick={resetGroupForm}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  {editingGroup ? 'Update Group' : 'Create Group'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modifier Form Modal */}
      {showModifierForm && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>{editingModifier ? 'Edit Modifier' : 'Add Modifier'}</h3>
              <button className="close-btn" onClick={resetModifierForm}>
                <Icon name="cancel" size="sm" />
              </button>
            </div>

            <form onSubmit={handleModifierSubmit} className="modifier-form">
              <div className="form-group">
                <label htmlFor="modifierName">Modifier Name *</label>
                <input
                  id="modifierName"
                  type="text"
                  value={modifierForm.name}
                  onChange={(e) => setModifierForm({ ...modifierForm, name: e.target.value })}
                  placeholder="e.g., Extra Cheese, Large Size"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="modifierPrice">Additional Price (₹)</label>
                <input
                  id="modifierPrice"
                  type="number"
                  step="0.01"
                  min="0"
                  value={modifierForm.price}
                  onChange={(e) => setModifierForm({ ...modifierForm, price: e.target.value })}
                  placeholder="0.00"
                />
              </div>

              <div className="form-group">
                <label htmlFor="modifierDescription">Description</label>
                <textarea
                  id="modifierDescription"
                  value={modifierForm.description}
                  onChange={(e) => setModifierForm({ ...modifierForm, description: e.target.value })}
                  placeholder="Optional description for this modifier"
                  rows={3}
                />
              </div>

              <div className="form-group">
                <label htmlFor="modifierDisplayOrder">Display Order</label>
                <input
                  id="modifierDisplayOrder"
                  type="number"
                  min="0"
                  value={modifierForm.displayOrder}
                  onChange={(e) => setModifierForm({ ...modifierForm, displayOrder: parseInt(e.target.value) || 0 })}
                  placeholder="Order in which this modifier appears"
                />
              </div>

              <div className="form-actions">
                <button type="button" className="btn btn-secondary" onClick={resetModifierForm}>
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  {editingModifier ? 'Update Modifier' : 'Create Modifier'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModifierManagement;
