import React from 'react';

interface POSWindowControlsProps {
  hasActiveOrder?: boolean;
  orderItemCount?: number;
}

const POSWindowControls: React.FC<POSWindowControlsProps> = ({
  hasActiveOrder = false,
  orderItemCount = 0,
}) => {
  const handleMinimize = () => {
    if (window.electronAPI && window.electronAPI.minimizeWindow) {
      window.electronAPI.minimizeWindow();
    }
  };

  const handleMaximize = () => {
    if (window.electronAPI && window.electronAPI.maximizeWindow) {
      window.electronAPI.maximizeWindow();
    }
  };

  const handleClose = () => {
    // Show confirmation if there's an active order
    if (hasActiveOrder) {
      const confirmed = window.confirm(
        'You have items in your current order. Are you sure you want to close this Order Taking window? Any unsaved changes will be lost.'
      );
      if (!confirmed) {
        return;
      }
    }

    if (window.electronAPI && (window.electronAPI as any).closePOSWindowSelf) {
      (window.electronAPI as any).closePOSWindowSelf();
    }
  };

  return (
    <div className="pos-window-header">
      <div className="pos-window-title">
        <span className="pos-title-text">
          Zyka POS - Order Taking
          {hasActiveOrder && <span className="active-order-indicator">●</span>}
        </span>
        <span className="pos-subtitle">
          {hasActiveOrder
            ? `Active Order - ${orderItemCount} item${orderItemCount !== 1 ? 's' : ''}`
            : 'Order Taking System'}
        </span>
      </div>
      <div className="pos-window-controls">
        <button
          className="pos-control-btn minimize"
          onClick={handleMinimize}
          title="Minimize Window"
        >
          <svg width="12" height="12" viewBox="0 0 12 12">
            <rect x="2" y="5" width="8" height="2" fill="currentColor" />
          </svg>
        </button>
        <button
          className="pos-control-btn maximize"
          onClick={handleMaximize}
          title="Maximize Window"
        >
          <svg width="12" height="12" viewBox="0 0 12 12">
            <rect
              x="2"
              y="2"
              width="8"
              height="8"
              stroke="currentColor"
              strokeWidth="1.5"
              fill="none"
            />
          </svg>
        </button>
        <button className="pos-control-btn close" onClick={handleClose} title="Close Window">
          <svg width="12" height="12" viewBox="0 0 12 12">
            <path
              d="M2 2L10 10M10 2L2 10"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default POSWindowControls;
