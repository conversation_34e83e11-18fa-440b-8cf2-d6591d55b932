import React, { useState } from 'react';
import {
  generateTestMenuItems,
  generateTestTables,
  runPerformanceTests,
} from '../utils/testDataGenerator';
import { MenuItem, Table } from '../types';
import Icon from './Icon';

interface PerformanceTestPanelProps {
  onTestDataGenerated?: (menuItems: MenuItem[], tables: Table[]) => void;
  onClose: () => void;
}

const PerformanceTestPanel: React.FC<PerformanceTestPanelProps> = ({
  onTestDataGenerated,
  onClose,
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);
  const [menuItemCount, setMenuItemCount] = useState(100);
  const [tableCount, setTableCount] = useState(50);

  const generateTestData = async () => {
    setIsGenerating(true);
    setTestResults([]);

    try {
      // Capture console.log output for test results
      const originalLog = console.log;
      const logs: string[] = [];
      console.log = (...args) => {
        logs.push(args.join(' '));
        originalLog(...args);
      };

      // Generate test data
      const menuItems = generateTestMenuItems(menuItemCount).map((item, index) => ({
        ...item,
        id: `test-menu-${index}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })) as MenuItem[];

      const tables = generateTestTables(tableCount).map((table, index) => ({
        ...table,
        id: `test-table-${index}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })) as Table[];

      // Run performance tests
      runPerformanceTests(menuItems, tables);

      // Restore console.log
      console.log = originalLog;
      setTestResults(logs);

      // Notify parent component
      if (onTestDataGenerated) {
        onTestDataGenerated(menuItems, tables);
      }
    } catch (error) {
      console.error('Error generating test data:', error);
      setTestResults(['Error generating test data: ' + error]);
    } finally {
      setIsGenerating(false);
    }
  };

  const clearTestData = () => {
    setTestResults([]);
    if (onTestDataGenerated) {
      onTestDataGenerated([], []);
    }
  };

  return (
    <div className="performance-test-overlay">
      <div className="performance-test-panel">
        <div className="test-header">
          <h3>Performance Test Panel</h3>
          <button className="close-btn" onClick={onClose}>
            <Icon name="cancel" size="sm" />
          </button>
        </div>

        <div className="test-content">
          <div className="test-description">
            <p>
              This panel generates test data to validate POS performance with large datasets. Use
              this to ensure the system can handle busy restaurant operations.
            </p>
          </div>

          <div className="test-controls">
            <div className="control-group">
              <label htmlFor="menuItemCount">Menu Items:</label>
              <input
                id="menuItemCount"
                type="number"
                min="10"
                max="1000"
                value={menuItemCount}
                onChange={e => setMenuItemCount(parseInt(e.target.value) || 100)}
                className="control-input"
              />
              <span className="control-hint">Recommended: 100-500 for testing</span>
            </div>

            <div className="control-group">
              <label htmlFor="tableCount">Tables:</label>
              <input
                id="tableCount"
                type="number"
                min="5"
                max="200"
                value={tableCount}
                onChange={e => setTableCount(parseInt(e.target.value) || 50)}
                className="control-input"
              />
              <span className="control-hint">Recommended: 50-100 for testing</span>
            </div>
          </div>

          <div className="test-actions">
            <button className="generate-btn" onClick={generateTestData} disabled={isGenerating}>
              {isGenerating ? (
                <>
                  <div className="loading-spinner small"></div>
                  Generating...
                </>
              ) : (
                <>
                  <Icon name="plus" size="sm" />
                  Generate Test Data
                </>
              )}
            </button>

            <button className="clear-btn" onClick={clearTestData} disabled={isGenerating}>
              <Icon name="trash" size="sm" />
              Clear Test Data
            </button>
          </div>

          {testResults.length > 0 && (
            <div className="test-results">
              <h4>Performance Test Results:</h4>
              <div className="results-log">
                {testResults.map((result, index) => (
                  <div key={index} className="log-line">
                    {result}
                  </div>
                ))}
              </div>

              <div className="results-summary">
                <h5>Performance Guidelines:</h5>
                <ul>
                  <li>Menu filtering should complete in &lt;10ms for good performance</li>
                  <li>Table operations should complete in &lt;5ms</li>
                  <li>Virtualization calculations should be &lt;1ms</li>
                  <li>Search operations should feel instant (&lt;100ms)</li>
                </ul>
              </div>
            </div>
          )}

          <div className="test-info">
            <div className="info-section">
              <h5>What this tests:</h5>
              <ul>
                <li>Menu item filtering and search performance</li>
                <li>Table management operations</li>
                <li>Virtualization efficiency</li>
                <li>Memory usage with large datasets</li>
              </ul>
            </div>

            <div className="info-section">
              <h5>Expected performance:</h5>
              <ul>
                <li>Smooth scrolling with 500+ menu items</li>
                <li>Instant search results</li>
                <li>Fast table selection with 100+ tables</li>
                <li>Responsive UI during busy periods</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceTestPanel;
