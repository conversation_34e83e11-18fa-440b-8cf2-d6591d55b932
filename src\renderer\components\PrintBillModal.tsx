import React, { useEffect, useState } from 'react';
import { Order } from '../types';
import { billingService } from '../services/billingService';

interface PrintBillModalProps {
  order: Order;
  onClose: () => void;
}

const PrintBillModal: React.FC<PrintBillModalProps> = ({ order, onClose }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [billHTML, setBillHTML] = useState('');

  useEffect(() => {
    const loadBillingSettings = async () => {
      try {
        setIsLoading(true);
        // Extract restaurant ID from order or use a default
        const restaurantId = (order as any).restaurantId || 'default';

        // Always refresh restaurant details to get latest data
        await billingService.refreshRestaurantDetails(restaurantId);
        await billingService.loadBillingSettings(restaurantId);

        // Generate bill HTML using billing service with latest data
        const html = billingService.generateBillHTML(order);
        setBillHTML(html);
      } catch (error) {
        console.error('Error loading billing settings:', error);
        // Fallback to simple bill
        setBillHTML(generateFallbackBillHTML());
      } finally {
        setIsLoading(false);
      }
    };

    loadBillingSettings();
  }, [order]);

  const generateFallbackBillHTML = () => {
    const timestamp = new Date().toLocaleString('en-IN');

    return `
      <div style="font-family: monospace; max-width: 300px; margin: 0 auto; padding: 20px; border: 1px solid #ccc;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h2 style="margin: 0;">RESTAURANT BILL</h2>
          <p style="margin: 5px 0;">Order: ${order.orderNumber}</p>
          <p style="margin: 5px 0;">Date: ${timestamp}</p>
          ${order.tableNumber ? `<p style="margin: 5px 0;">Table: ${order.tableNumber}</p>` : ''}
          <p style="margin: 5px 0;">Type: ${order.orderType.toUpperCase()}</p>
        </div>

        <div style="border-top: 1px dashed #000; padding-top: 10px; margin-bottom: 10px;">
          <h3 style="margin: 0 0 10px 0;">ITEMS:</h3>
          ${order.items
            .map(
              item => `
            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
              <span>${item.quantity}x ${item.menuItemName}</span>
              <span>₹${item.subtotal.toFixed(2)}</span>
            </div>
          `
            )
            .join('')}
        </div>

        <div style="border-top: 1px dashed #000; padding-top: 10px;">
          <div style="display: flex; justify-content: space-between; font-weight: bold; font-size: 1.2em;">
            <span>TOTAL:</span>
            <span>₹${order.totalAmount.toFixed(2)}</span>
          </div>
        </div>

        <div style="text-align: center; margin-top: 20px; padding-top: 10px; border-top: 1px dashed #000;">
          <p style="margin: 5px 0; font-weight: bold; color: red;">STATUS: UNPAID</p>
          <p style="margin: 5px 0;">Thank you for your visit!</p>
        </div>
      </div>
    `;
  };

  const handlePrint = async () => {
    try {
      // Always refresh billing settings before printing to get latest data
      const restaurantId = (order as any).restaurantId || 'default';
      await billingService.refreshRestaurantDetails(restaurantId);
      await billingService.loadBillingSettings(restaurantId);

      billingService.printBill(order);
    } catch (error) {
      console.error('Error printing bill:', error);
      // Fallback to simple print
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>Bill - ${order.orderNumber}</title>
              <style>
                body { margin: 0; padding: 20px; }
                @media print {
                  body { margin: 0; }
                }
              </style>
            </head>
            <body>
              ${billHTML || generateFallbackBillHTML()}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content" style={{ maxWidth: '500px', width: '90%' }}>
        <div className="modal-header">
          <h2>Print Bill - {order.orderNumber}</h2>
          <button className="close-btn" onClick={onClose}>
            ×
          </button>
        </div>

        <div className="modal-body">
          {isLoading ? (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <p>Loading billing settings...</p>
            </div>
          ) : (
            <div
              className="bill-preview"
              style={{
                maxHeight: '400px',
                overflow: 'auto',
                border: '1px solid #ddd',
                padding: '10px',
              }}
            >
              <div dangerouslySetInnerHTML={{ __html: billHTML }} />
            </div>
          )}
        </div>

        <div
          className="modal-actions"
          style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end', marginTop: '20px' }}
        >
          <button className="btn btn-secondary" onClick={onClose}>
            Close
          </button>
          <button className="btn btn-primary" onClick={handlePrint}>
            🖨️ Print
          </button>
        </div>
      </div>
    </div>
  );
};

export default PrintBillModal;
