import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { MenuItem, Table, OrderItem, Order, SelectedModifier } from '../types';
import { useMenu } from '../contexts/MenuContext';
import { useNotifications } from '../contexts/NotificationContext';
import { eventBus, EVENTS } from '../utils/eventBus';
import Icon from './Icon';
import MenuSelectionModal from './MenuSelectionModal';

interface RedesignedPOSProps {
  restaurantId: string;
}

type OrderType = 'dine-in' | 'takeaway' | 'delivery';

interface CustomerInfo {
  name: string;
  phone: string;
  address: string;
}

const RedesignedPOS: React.FC<RedesignedPOSProps> = ({ restaurantId }) => {
  // Core state
  const { menuItems, isLoading } = useMenu();
  const { showToast } = useNotifications();
  const [tables, setTables] = useState<Table[]>([]);
  const [currentOrder, setCurrentOrder] = useState<OrderItem[]>([]);
  const [orderType, setOrderType] = useState<OrderType>('dine-in');
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: '',
    phone: '',
    address: '',
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showMenuModal, setShowMenuModal] = useState(false);
  const [modalSelectedTable, setModalSelectedTable] = useState<Table | null>(null);

  // Load tables on component mount
  useEffect(() => {
    loadTables();
  }, [restaurantId]);

  const loadTables = async () => {
    try {
      const tablesData = await window.electronAPI.getTables(restaurantId);
      setTables(tablesData || []);
    } catch (error) {
      console.error('Error loading tables:', error);
    }
  };

  // Get unique categories from menu items
  const categories = useMemo(() => {
    const cats = ['all', ...new Set(menuItems.map(item => item.category).filter(Boolean))] as string[];
    return cats;
  }, [menuItems]);

  // Filter menu items based on search and category
  const filteredMenuItems = useMemo(() => {
    return menuItems.filter(item => {
      const matchesSearch = !searchQuery || 
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.code?.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
      
      return matchesSearch && matchesCategory && item.available;
    });
  }, [menuItems, searchQuery, selectedCategory]);

  // Handle order type change
  const handleOrderTypeChange = (type: OrderType) => {
    setOrderType(type);
    setSelectedTable(null);
    // Clear customer info when switching to dine-in
    if (type === 'dine-in') {
      setCustomerInfo({ name: '', phone: '', address: '' });
    }
  };

  // Handle table selection for dine-in
  const handleTableSelect = (table: Table) => {
    if (table.status === 'available') {
      setModalSelectedTable(table);
      setShowMenuModal(true);
    }
  };

  // Add item to order
  const addToOrder = useCallback((item: MenuItem, modifiers?: SelectedModifier[], notes?: string, quantity: number = 1) => {
    setCurrentOrder(prevOrder => {
      // Calculate modifier price
      const modifierPrice = modifiers?.reduce((sum, modifier) => sum + modifier.price, 0) || 0;
      const itemTotalPrice = item.price + modifierPrice;

      // For items with modifiers, always create a new order item (don't merge)
      // For items without modifiers, merge with existing if same item
      const shouldMerge = !modifiers || modifiers.length === 0;

      if (shouldMerge) {
        const existingItem = prevOrder.find(orderItem =>
          orderItem.menuItemId === item.id &&
          (!orderItem.modifiers || orderItem.modifiers.length === 0) &&
          orderItem.notes === (notes || '')
        );

        if (existingItem) {
          return prevOrder.map(orderItem =>
            orderItem.id === existingItem.id
              ? {
                  ...orderItem,
                  quantity: orderItem.quantity + quantity,
                  subtotal: (orderItem.quantity + quantity) * orderItem.menuItemPrice
                }
              : orderItem
          );
        }
      }

      // Create new order item
      const newOrderItem: OrderItem = {
        id: `order_item_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        menuItemId: item.id,
        menuItemName: item.name,
        menuItemPrice: itemTotalPrice,
        quantity: quantity,
        subtotal: itemTotalPrice * quantity,
        notes: notes || '',
        status: 'pending',
        modifiers: modifiers || [],
      };
      return [...prevOrder, newOrderItem];
    });
  }, []);

  // Update item quantity
  const updateQuantity = useCallback((itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      setCurrentOrder(prevOrder => prevOrder.filter(item => item.id !== itemId));
    } else {
      setCurrentOrder(prevOrder =>
        prevOrder.map(item =>
          item.id === itemId
            ? {
                ...item,
                quantity: newQuantity,
                subtotal: newQuantity * item.menuItemPrice
              }
            : item
        )
      );
    }
  }, []);

  // Clear order
  const clearOrder = () => {
    setCurrentOrder([]);
    setSelectedTable(null);
    setModalSelectedTable(null);
  };

  // Calculate order total
  const calculateTotal = () => {
    return currentOrder.reduce((total, item) => total + item.subtotal, 0);
  };

  // Handle KOT (Kitchen Order Ticket) generation
  const handlePunchKOT = async () => {
    if (currentOrder.length === 0) {
      showToast({
        type: 'warning',
        title: 'Empty Order',
        message: 'Please add items to the order first'
      });
      return;
    }

    // Validate required fields for delivery
    if (orderType === 'delivery') {
      if (!customerInfo.name || !customerInfo.phone || !customerInfo.address) {
        showToast({
          type: 'warning',
          title: 'Missing Information',
          message: 'Please fill in all customer information fields for delivery orders'
        });
        return;
      }
    }

    try {
      const orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'> = {
        orderNumber: `ORD${Date.now()}`,
        restaurantId,
        orderType,
        tableId: orderType === 'dine-in' ? (selectedTable?.id || modalSelectedTable?.id) : undefined,
        tableNumber: orderType === 'dine-in' ? (selectedTable?.tableNumber || modalSelectedTable?.tableNumber) : undefined,
        customerName: customerInfo.name || undefined,
        customerPhone: customerInfo.phone || undefined,
        notes: orderType === 'delivery' ? `Address: ${customerInfo.address}` : undefined,
        status: 'pending',
        items: currentOrder,
        subtotal: calculateTotal(),
        taxAmount: 0, // Will be calculated by backend
        totalAmount: calculateTotal(),
        paymentStatus: 'pending',
        paymentMethod: undefined,
        kotPrinted: false,
        billPrinted: false,
      };

      const result = await window.electronAPI.createOrder(orderData);
      
      if (result.success && result.order) {
        // Update table status if dine-in
        if (orderType === 'dine-in' && (selectedTable || modalSelectedTable)) {
          const tableToUpdate = selectedTable || modalSelectedTable;
          await window.electronAPI.updateTable(tableToUpdate!.id, {
            status: 'occupied',
            currentOrderId: result.order.id
          });
          await loadTables();
        }

        // Clear the order
        clearOrder();

        // Show success message
        showToast({
          type: 'success',
          title: 'Order Created',
          message: `Order ${result.order.orderNumber} has been sent to kitchen!`
        });

        // Emit event for other components
        eventBus.emit(EVENTS.ORDER_CREATED, result.order);
      } else {
        showToast({
          type: 'error',
          title: 'Order Failed',
          message: 'Failed to create order: ' + (result.error || 'Unknown error')
        });
      }
    } catch (error) {
      console.error('Error creating order:', error);
      showToast({
        type: 'error',
        title: 'Order Failed',
        message: 'Failed to create order'
      });
    }
  };

  // Handle menu modal confirmation
  const handleMenuModalConfirm = () => {
    setSelectedTable(modalSelectedTable);
    setShowMenuModal(false);
    setModalSelectedTable(null);
  };

  // Handle menu modal close
  const handleMenuModalClose = () => {
    setShowMenuModal(false);
    setModalSelectedTable(null);
  };

  if (isLoading) {
    return (
      <div className="redesigned-pos-loading">
        <div className="loading-spinner"></div>
        <p>Loading POS system...</p>
      </div>
    );
  }

  return (
    <div className="redesigned-pos">
      {/* Header Section */}
      <div className="pos-header-new">
        {/* Order Type Buttons */}
        <div className="order-type-buttons">
          {(['dine-in', 'takeaway', 'delivery'] as OrderType[]).map(type => (
            <button
              key={type}
              className={`order-type-btn-new ${orderType === type ? 'active' : ''}`}
              onClick={() => handleOrderTypeChange(type)}
              onTouchEnd={(e) => {
                e.preventDefault();
                handleOrderTypeChange(type);
              }}
            >
              <Icon
                name={
                  type === 'dine-in'
                    ? 'utensils'
                    : type === 'takeaway'
                      ? 'shopping-cart'
                      : 'delivery'
                }
                size="sm"
              />
              {type === 'dine-in' ? 'Dine-In' : type === 'takeaway' ? 'Takeaway' : 'Delivery'}
            </button>
          ))}
        </div>

        {/* Customer Information Fields */}
        <div className="customer-info-section">
          <div className="customer-field">
            <label>Name</label>
            <input
              type="text"
              value={customerInfo.name}
              onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
              placeholder={orderType === 'delivery' ? 'Required' : 'Optional'}
              className={orderType === 'delivery' ? 'required' : ''}
            />
          </div>
          <div className="customer-field">
            <label>Phone</label>
            <input
              type="tel"
              value={customerInfo.phone}
              onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
              placeholder={orderType === 'delivery' ? 'Required' : 'Optional'}
              className={orderType === 'delivery' ? 'required' : ''}
            />
          </div>
          <div className="customer-field">
            <label>Address</label>
            <input
              type="text"
              value={customerInfo.address}
              onChange={(e) => setCustomerInfo(prev => ({ ...prev, address: e.target.value }))}
              placeholder={orderType === 'delivery' ? 'Required' : 'Optional'}
              className={orderType === 'delivery' ? 'required' : ''}
            />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="pos-main-content">
        {/* Left Panel */}
        <div className="pos-left-panel">
          {orderType === 'dine-in' ? (
            // Table Matrix for Dine-In
            <div className="table-matrix-section">
              <h3>Select Table</h3>
              <div className="table-matrix">
                {tables.map(table => (
                  <div
                    key={table.id}
                    className={`table-card-new ${table.status}`}
                    onClick={() => handleTableSelect(table)}
                    onTouchEnd={(e) => {
                      e.preventDefault();
                      handleTableSelect(table);
                    }}
                  >
                    <div className="table-number">T{table.tableNumber}</div>
                    <div className="table-capacity">{table.capacity} pax</div>
                    {table.location && <div className="table-area">{table.location}</div>}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            // Menu Items for Takeaway and Delivery
            <div className="menu-section-new">
              <div className="menu-controls-new">
                <div className="search-section-new">
                  <input
                    type="text"
                    placeholder="Search menu items..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="search-input-new"
                  />
                  <button className="search-btn-new">
                    <Icon name="search" size="sm" />
                  </button>
                </div>
                
                <div className="category-tabs-new">
                  {categories.map(category => (
                    <button
                      key={category}
                      className={`category-tab-new ${selectedCategory === category ? 'active' : ''}`}
                      onClick={() => setSelectedCategory(category)}
                      onTouchEnd={(e) => {
                        e.preventDefault();
                        setSelectedCategory(category);
                      }}
                    >
                      {category === 'all' ? 'All' : (category || 'Uncategorized')}
                    </button>
                  ))}
                </div>
              </div>

              <div className="menu-items-list-new">
                {filteredMenuItems.map(item => (
                  <div
                    key={item.id}
                    className="menu-item-new"
                    onClick={() => addToOrder(item)}
                    onTouchEnd={(e) => {
                      e.preventDefault();
                      addToOrder(item);
                    }}
                  >
                    <div className="item-info-new">
                      <div className="item-name-new">{item.name}</div>
                      <div className="item-price-new">₹{item.price.toFixed(2)}</div>
                      {item.description && (
                        <div className="item-description-new">{item.description}</div>
                      )}
                      {item.code && (
                        <div className="item-code-new">Code: {item.code}</div>
                      )}
                    </div>
                    <div className="quantity-controls-new">
                      <button
                        className="qty-btn-new decrease"
                        onClick={(e) => {
                          e.stopPropagation();
                          const existingItem = currentOrder.find(orderItem => orderItem.menuItemId === item.id);
                          if (existingItem) {
                            updateQuantity(existingItem.id, existingItem.quantity - 1);
                          }
                        }}
                        onTouchEnd={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          const existingItem = currentOrder.find(orderItem => orderItem.menuItemId === item.id);
                          if (existingItem) {
                            updateQuantity(existingItem.id, existingItem.quantity - 1);
                          }
                        }}
                      >
                        -
                      </button>
                      <span className="quantity-display-new">
                        {currentOrder.find(orderItem => orderItem.menuItemId === item.id)?.quantity || 0}
                      </span>
                      <button
                        className="qty-btn-new increase"
                        onClick={(e) => {
                          e.stopPropagation();
                          addToOrder(item);
                        }}
                        onTouchEnd={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          addToOrder(item);
                        }}
                      >
                        +
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Right Panel - Current Order */}
        <div className="pos-right-panel">
          <div className="current-order-section">
            <h3>Current Order</h3>
            
            {selectedTable && (
              <div className="selected-table-info">
                <Icon name="utensils" size="sm" />
                <span>Table {selectedTable.tableNumber}</span>
              </div>
            )}

            <div className="order-items-list">
              {currentOrder.length === 0 ? (
                <div className="empty-order-new">
                  <Icon name="shopping-cart" size="xl" />
                  <p>No items added</p>
                </div>
              ) : (
                currentOrder.map((item, index) => (
                  <div key={item.id} className="order-item-new">
                    <div className="item-details-new">
                      <span className="item-name-new">{item.menuItemName}</span>
                      <span className="item-price-new">₹{item.menuItemPrice} each</span>
                    </div>
                    <div className="quantity-controls-new">
                      <button
                        className="qty-btn-new decrease"
                        onClick={() => updateQuantity(item.id, item.quantity - 1)}
                      >
                        -
                      </button>
                      <span className="quantity-display-new">{item.quantity}</span>
                      <button
                        className="qty-btn-new increase"
                        onClick={() => updateQuantity(item.id, item.quantity + 1)}
                      >
                        +
                      </button>
                    </div>
                    <div className="item-subtotal-new">₹{item.subtotal.toFixed(2)}</div>
                  </div>
                ))
              )}
            </div>

            {currentOrder.length > 0 && (
              <div className="order-total-section">
                <div className="total-amount">
                  <strong>Total: ₹{calculateTotal().toFixed(2)}</strong>
                </div>
              </div>
            )}

            <div className="order-actions-new">
              <button
                className="clear-btn-new"
                onClick={clearOrder}
                onTouchEnd={(e) => {
                  e.preventDefault();
                  if (currentOrder.length > 0) {
                    clearOrder();
                  }
                }}
                disabled={currentOrder.length === 0}
              >
                Clear
              </button>
              <button
                className="kot-btn-new"
                onClick={handlePunchKOT}
                onTouchEnd={(e) => {
                  e.preventDefault();
                  if (currentOrder.length > 0) {
                    handlePunchKOT();
                  }
                }}
                disabled={currentOrder.length === 0}
              >
                Punch KOT
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Menu Selection Modal */}
      {showMenuModal && modalSelectedTable && (
        <MenuSelectionModal
          table={modalSelectedTable}
          menuItems={filteredMenuItems}
          categories={categories}
          currentOrder={currentOrder}
          onAddToOrder={addToOrder}
          onUpdateQuantity={updateQuantity}
          onClose={handleMenuModalClose}
          onConfirm={handleMenuModalConfirm}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          selectedCategory={selectedCategory}
          setSelectedCategory={setSelectedCategory}
        />
      )}
    </div>
  );
};

export default RedesignedPOS;
