import React, { useState, useEffect } from 'react';
import { Order } from '../types';
import { eventBus, EVENTS } from '../utils/eventBus';
import { billingService } from '../services/billingService';

interface SettleBillModalProps {
  order: Order;
  onClose: () => void;
  onSettled: () => void;
}

type PaymentMethod = 'cash' | 'card' | 'online';

const SettleBillModal: React.FC<SettleBillModalProps> = ({ order, onClose, onSettled }) => {
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('cash');
  const [amountReceived, setAmountReceived] = useState<string>(order.totalAmount.toString());
  const [isProcessing, setIsProcessing] = useState(false);
  const [transactionId, setTransactionId] = useState<string>('');
  const [billPreviewHTML, setBillPreviewHTML] = useState<string>('');

  // Load billing settings when component mounts
  useEffect(() => {
    const loadBillingSettings = async () => {
      try {
        const restaurantId = (order as any).restaurantId || 'default';

        // Always refresh restaurant details to get latest data
        await billingService.refreshRestaurantDetails(restaurantId);
        await billingService.loadBillingSettings(restaurantId);
      } catch (error) {
        console.error('Error loading billing settings:', error);
      }
    };

    loadBillingSettings();
  }, [order]);

  // Load bill preview when component mounts or payment details change
  useEffect(() => {
    const loadBillPreview = async () => {
      try {
        const html = await generateSettledBillHTML();
        setBillPreviewHTML(html);
      } catch (error) {
        console.error('Error loading bill preview:', error);
        setBillPreviewHTML('<p>Error loading bill preview</p>');
      }
    };

    loadBillPreview();
  }, [paymentMethod, amountReceived, transactionId]);

  const calculateChange = () => {
    if (paymentMethod === 'cash') {
      const received = parseFloat(amountReceived) || 0;
      const change = received - order.totalAmount;
      return change > 0 ? change : 0;
    }
    return 0;
  };

  const generateSettledBillHTML = async () => {
    try {
      // Always refresh billing settings before generating bill
      const restaurantId = (order as any).restaurantId || 'default';
      await billingService.refreshRestaurantDetails(restaurantId);
      await billingService.loadBillingSettings(restaurantId);

      const paymentDetails = {
        method: paymentMethod,
        amountReceived: parseFloat(amountReceived),
        transactionId: transactionId || undefined,
      };

      return billingService.generateBillHTML(order, paymentDetails);
    } catch (error) {
      console.error('Error generating settled bill:', error);
      // Fallback to simple bill
      const timestamp = new Date().toLocaleString('en-IN');
      const paymentMethodDisplay =
        paymentMethod === 'cash' ? 'CASH' : paymentMethod === 'card' ? 'CARD' : 'ONLINE';

      return `
        <div style="font-family: monospace; max-width: 300px; margin: 0 auto; padding: 20px; border: 1px solid #ccc;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="margin: 0;">PAYMENT RECEIPT</h2>
            <p style="margin: 5px 0;">Order: ${order.orderNumber}</p>
            <p style="margin: 5px 0;">Date: ${timestamp}</p>
            ${order.tableNumber ? `<p style="margin: 5px 0;">Table: ${order.tableNumber}</p>` : ''}
            <p style="margin: 5px 0;">Type: ${order.orderType.toUpperCase()}</p>
          </div>

          <div style="border-top: 1px dashed #000; padding-top: 10px; margin-bottom: 10px;">
            <h3 style="margin: 0 0 10px 0;">ITEMS:</h3>
            ${order.items
              .map(
                item => `
              <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                <span>${item.quantity}x ${item.menuItemName}</span>
                <span>₹${item.subtotal.toFixed(2)}</span>
              </div>
            `
              )
              .join('')}
          </div>

          <div style="border-top: 1px dashed #000; padding-top: 10px; margin-bottom: 10px;">
            <div style="display: flex; justify-content: space-between; font-weight: bold; font-size: 1.2em;">
              <span>TOTAL:</span>
              <span>₹${order.totalAmount.toFixed(2)}</span>
            </div>
          </div>

          <div style="border-top: 1px dashed #000; padding-top: 10px; margin-bottom: 10px;">
            <h3 style="margin: 0 0 10px 0;">PAYMENT:</h3>
            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
              <span>Method:</span>
              <span>${paymentMethodDisplay}</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
              <span>Amount Received:</span>
              <span>₹${parseFloat(amountReceived).toFixed(2)}</span>
            </div>
            ${
              paymentMethod === 'cash' && parseFloat(amountReceived) > order.totalAmount
                ? `
            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
              <span>Change Given:</span>
              <span>₹${(parseFloat(amountReceived) - order.totalAmount).toFixed(2)}</span>
            </div>
            `
                : ''
            }
          </div>

          <div style="text-align: center; margin-top: 20px; padding-top: 10px; border-top: 1px dashed #000;">
            <p style="margin: 5px 0; font-weight: bold; color: green;">STATUS: PAID</p>
            <p style="margin: 5px 0;">Thank you for your visit!</p>
          </div>
        </div>
      `;
    }
  };

  const handlePrint = async () => {
    try {
      const settledBillHTML = await generateSettledBillHTML();
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>Bill - ${order.orderNumber}</title>
              <style>
                body { margin: 0; padding: 20px; }
                @media print {
                  body { margin: 0; }
                }
              </style>
            </head>
            <body>
              ${settledBillHTML}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    } catch (error) {
      console.error('Error printing settled bill:', error);
    }
  };

  const handleSettle = async () => {
    setIsProcessing(true);

    try {
      // Update order payment status
      await (window.electronAPI as any).updateOrder(order.id, {
        paymentStatus: 'paid',
        paymentMethod: paymentMethod,
        billPrinted: true,
        status: 'completed',
      });

      // If it's a dine-in order, mark table as available
      if (order.orderType === 'dine-in' && order.tableId) {
        await (window.electronAPI as any).updateTable(order.tableId, { status: 'available' });
        eventBus.emit(EVENTS.TABLE_UPDATED);
      }

      // Emit events for dashboard refresh
      eventBus.emit(EVENTS.PAYMENT_COMPLETED, { order, paymentMethod });
      eventBus.emit(EVENTS.ORDER_UPDATED, order);
      eventBus.emit(EVENTS.DASHBOARD_REFRESH);

      // Print the settled bill
      await handlePrint();

      alert(`Order ${order.orderNumber} settled successfully!`);
      onSettled();
    } catch (error) {
      console.error('Settlement failed:', error);
      alert('Settlement failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const isValidPayment = () => {
    if (paymentMethod === 'cash') {
      const received = parseFloat(amountReceived) || 0;
      return received >= order.totalAmount;
    }
    return true; // Card and online payments are always valid
  };

  return (
    <div className="modal-overlay">
      <div
        className="modal-content"
        style={{ maxWidth: '900px', width: '95%', minHeight: '600px' }}
      >
        <div className="modal-header">
          <h2>Settle Bill - {order.orderNumber}</h2>
          <button className="close-btn" onClick={onClose}>
            ×
          </button>
        </div>

        <div className="modal-body" style={{ display: 'flex', gap: '20px' }}>
          <div style={{ flex: 1 }}>
            <h3>Payment Details</h3>

            <div style={{ marginBottom: '20px' }}>
              <label style={{ display: 'block', marginBottom: '10px', fontWeight: 'bold' }}>
                Payment Method:
              </label>
              <div style={{ display: 'flex', gap: '10px', marginBottom: '15px' }}>
                {(['cash', 'card', 'online'] as PaymentMethod[]).map(method => (
                  <button
                    key={method}
                    className={`btn ${paymentMethod === method ? 'btn-primary' : 'btn-secondary'}`}
                    onClick={() => setPaymentMethod(method)}
                    style={{ textTransform: 'capitalize' }}
                  >
                    {method}
                  </button>
                ))}
              </div>
            </div>

            {paymentMethod === 'cash' && (
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                  Amount Received:
                </label>
                <input
                  type="number"
                  value={amountReceived}
                  onChange={e => setAmountReceived(e.target.value)}
                  min={order.totalAmount}
                  step="0.01"
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                  }}
                />
                {parseFloat(amountReceived) > order.totalAmount && (
                  <p style={{ margin: '5px 0', color: '#666' }}>
                    Change: ₹{(parseFloat(amountReceived) - order.totalAmount).toFixed(2)}
                  </p>
                )}
              </div>
            )}

            {(paymentMethod === 'card' || paymentMethod === 'online') && (
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                  Transaction ID (Optional):
                </label>
                <input
                  type="text"
                  value={transactionId}
                  onChange={e => setTransactionId(e.target.value)}
                  placeholder="Enter transaction ID"
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                  }}
                />
              </div>
            )}

            <div style={{ padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  fontWeight: 'bold',
                  fontSize: '1.2em',
                }}
              >
                <span>Total Amount:</span>
                <span>₹{order.totalAmount.toFixed(2)}</span>
              </div>
            </div>
          </div>

          <div style={{ flex: 1 }}>
            <h3>Bill Preview</h3>
            <div
              style={{
                maxHeight: '500px',
                overflow: 'auto',
                border: '1px solid #ddd',
                padding: '15px',
                borderRadius: '8px',
                backgroundColor: '#fafafa',
              }}
            >
              <div dangerouslySetInnerHTML={{ __html: billPreviewHTML }} />
            </div>
          </div>
        </div>

        <div
          className="modal-actions"
          style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end', marginTop: '20px' }}
        >
          <button className="btn btn-secondary" onClick={onClose}>
            Close
          </button>
          <button
            className="btn btn-primary"
            onClick={handleSettle}
            disabled={!isValidPayment() || isProcessing}
          >
            {isProcessing ? 'Processing...' : 'Settle & Print'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettleBillModal;
