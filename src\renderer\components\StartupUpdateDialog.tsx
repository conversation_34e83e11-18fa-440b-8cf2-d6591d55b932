import React, { useState, useEffect } from 'react';
import Icon from './Icon';

interface UpdateInfo {
  version: string;
  releaseNotes?: string;
  releaseDate?: string;
}

interface UpdateStatus {
  checking: boolean;
  available: boolean;
  downloading: boolean;
  downloaded: boolean;
  error: string | null;
  progress: {
    percent: number;
    bytesPerSecond: number;
    total: number;
    transferred: number;
  } | null;
  updateInfo: UpdateInfo | null;
}

interface StartupUpdateDialogProps {
  onComplete: () => void;
  onSkip: () => void;
}

const StartupUpdateDialog: React.FC<StartupUpdateDialogProps> = ({ onComplete, onSkip }) => {
  const [status, setStatus] = useState<UpdateStatus>({
    checking: false,
    available: false,
    downloading: false,
    downloaded: false,
    error: null,
    progress: null,
    updateInfo: null
  });
  const [isUpdating, setIsUpdating] = useState(false);
  const [estimatedTime, setEstimatedTime] = useState<string>('');

  useEffect(() => {
    // Get initial status
    const getStatus = async () => {
      try {
        const result = await window.electronAPI.updaterGetStatus();
        if (result.success) {
          setStatus(result.status);
        }
      } catch (error) {
        console.error('Failed to get updater status:', error);
      }
    };

    getStatus();

    // Listen for updater events
    const handleUpdaterEvent = (event: { event: string; data?: any }) => {
      switch (event.event) {
        case 'update-checking':
          setStatus(prev => ({ ...prev, checking: true, error: null }));
          break;
        case 'update-available':
          setStatus(prev => ({ 
            ...prev, 
            checking: false, 
            available: true, 
            updateInfo: event.data 
          }));
          break;
        case 'update-not-available':
          setStatus(prev => ({ 
            ...prev, 
            checking: false, 
            available: false, 
            updateInfo: event.data 
          }));
          onComplete(); // No update available, continue
          break;
        case 'update-error':
          setStatus(prev => ({ 
            ...prev, 
            checking: false, 
            downloading: false, 
            error: event.data.message 
          }));
          break;
        case 'update-download-progress':
          setStatus(prev => ({ 
            ...prev, 
            downloading: true, 
            progress: event.data 
          }));
          updateEstimatedTime(event.data);
          break;
        case 'update-downloaded':
          setStatus(prev => ({ 
            ...prev, 
            downloading: false, 
            downloaded: true, 
            updateInfo: event.data 
          }));
          setIsUpdating(false);
          break;
      }
    };

    window.electronAPI.onUpdaterEvent(handleUpdaterEvent);

    return () => {
      // Cleanup listener if needed
    };
  }, [onComplete]);

  const updateEstimatedTime = (progress: any) => {
    if (progress.bytesPerSecond > 0) {
      const remainingBytes = progress.total - progress.transferred;
      const remainingSeconds = remainingBytes / progress.bytesPerSecond;
      
      if (remainingSeconds < 60) {
        setEstimatedTime(`${Math.ceil(remainingSeconds)} seconds`);
      } else {
        const minutes = Math.ceil(remainingSeconds / 60);
        setEstimatedTime(`${minutes} minute${minutes > 1 ? 's' : ''}`);
      }
    }
  };

  const handleUpdate = async () => {
    setIsUpdating(true);
    try {
      await window.electronAPI.updaterDownloadUpdate();
    } catch (error) {
      console.error('Failed to download update:', error);
      setIsUpdating(false);
    }
  };

  const handleInstall = async () => {
    try {
      await window.electronAPI.updaterQuitAndInstall();
    } catch (error) {
      console.error('Failed to install update:', error);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatSpeed = (bytesPerSecond: number): string => {
    return formatFileSize(bytesPerSecond) + '/s';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="p-6">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
              <Icon name="download" size="md" className="text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {status.checking && 'Checking for Updates'}
                {status.available && !status.downloading && !status.downloaded && 'Update Available'}
                {status.downloading && 'Downloading Update'}
                {status.downloaded && 'Update Ready'}
                {status.error && 'Update Error'}
              </h3>
              <p className="text-sm text-gray-500">Zyka POS</p>
            </div>
          </div>

          <div className="mb-6">
            {status.checking && (
              <div className="text-center">
                <div className="loading-spinner mx-auto mb-2"></div>
                <p className="text-gray-600">Checking for the latest version...</p>
              </div>
            )}

            {status.available && !status.downloading && !status.downloaded && status.updateInfo && (
              <div>
                <p className="text-gray-800 mb-3">
                  A new version <strong>{status.updateInfo.version}</strong> is available!
                </p>
                {status.updateInfo.releaseNotes && (
                  <div className="bg-gray-50 rounded-lg p-3 mb-4 max-h-32 overflow-y-auto">
                    <p className="text-sm text-gray-600 whitespace-pre-wrap">
                      {status.updateInfo.releaseNotes}
                    </p>
                  </div>
                )}
                <p className="text-sm text-gray-500 mb-4">
                  We recommend updating to get the latest features and security improvements.
                </p>
              </div>
            )}

            {status.downloading && status.progress && (
              <div>
                <p className="text-gray-800 mb-3">
                  Downloading update {status.updateInfo?.version}...
                </p>
                <div className="mb-3">
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>{Math.round(status.progress.percent)}% complete</span>
                    <span>{formatSpeed(status.progress.bytesPerSecond)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${status.progress.percent}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>{formatFileSize(status.progress.transferred)} / {formatFileSize(status.progress.total)}</span>
                    {estimatedTime && <span>~{estimatedTime} remaining</span>}
                  </div>
                </div>
                <p className="text-sm text-gray-600">
                  Please wait while the update downloads. Do not close the application.
                </p>
              </div>
            )}

            {status.downloaded && status.updateInfo && (
              <div>
                <div className="flex items-center mb-3">
                  <Icon name="check-circle" size="sm" className="text-green-600 mr-2" />
                  <p className="text-gray-800">
                    Update <strong>{status.updateInfo.version}</strong> is ready to install!
                  </p>
                </div>
                <p className="text-sm text-gray-600 mb-3">
                  The application will restart to complete the installation.
                </p>
              </div>
            )}

            {status.error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-center mb-2">
                  <Icon name="warning" size="sm" className="text-red-600 mr-2" />
                  <p className="text-red-800 font-medium">Update Error</p>
                </div>
                <p className="text-sm text-red-600">{status.error}</p>
              </div>
            )}
          </div>

          <div className="flex space-x-3">
            {status.available && !status.downloading && !status.downloaded && (
              <>
                <button
                  onClick={handleUpdate}
                  disabled={isUpdating}
                  className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isUpdating ? 'Starting Download...' : 'Update Now'}
                </button>
                <button
                  onClick={onSkip}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Skip
                </button>
              </>
            )}

            {status.downloaded && (
              <>
                <button
                  onClick={handleInstall}
                  className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                >
                  Restart & Install
                </button>
                <button
                  onClick={onComplete}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Later
                </button>
              </>
            )}

            {status.error && (
              <button
                onClick={onSkip}
                className="flex-1 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Continue Anyway
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StartupUpdateDialog;
