import React, { useState, useEffect } from 'react';

interface UpdateInfo {
  version: string;
  releaseDate: string;
  releaseName?: string;
  releaseNotes?: string;
}

interface UpdateProgress {
  percent: number;
  bytesPerSecond: number;
  total: number;
  transferred: number;
}

interface UpdateStatus {
  checking: boolean;
  available: boolean;
  downloading: boolean;
  downloaded: boolean;
  error: string | null;
  progress: UpdateProgress | null;
  updateInfo: UpdateInfo | null;
}

interface UpdateNotificationProps {
  onClose?: () => void;
}

const UpdateNotification: React.FC<UpdateNotificationProps> = ({ onClose }) => {
  const [status, setStatus] = useState<UpdateStatus>({
    checking: false,
    available: false,
    downloading: false,
    downloaded: false,
    error: null,
    progress: null,
    updateInfo: null
  });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Get initial status
    const getStatus = async () => {
      try {
        const result = await window.electronAPI.updaterGetStatus();
        if (result.success) {
          setStatus(result.status);
          setIsVisible(result.status.available || result.status.downloading || result.status.downloaded);
        }
      } catch (error) {
        console.error('Failed to get updater status:', error);
      }
    };

    getStatus();

    // Listen for updater events
    const handleUpdaterEvent = (event: { event: string; data?: any }) => {
      switch (event.event) {
        case 'update-checking':
          setStatus(prev => ({ ...prev, checking: true, error: null }));
          break;
        case 'update-available':
          setStatus(prev => ({ 
            ...prev, 
            checking: false, 
            available: true, 
            updateInfo: event.data 
          }));
          setIsVisible(true);
          break;
        case 'update-not-available':
          setStatus(prev => ({ 
            ...prev, 
            checking: false, 
            available: false, 
            updateInfo: event.data 
          }));
          break;
        case 'update-error':
          setStatus(prev => ({ 
            ...prev, 
            checking: false, 
            downloading: false, 
            error: event.data.message 
          }));
          setIsVisible(true);
          break;
        case 'update-download-progress':
          setStatus(prev => ({ 
            ...prev, 
            downloading: true, 
            progress: event.data 
          }));
          setIsVisible(true);
          break;
        case 'update-downloaded':
          setStatus(prev => ({ 
            ...prev, 
            downloading: false, 
            downloaded: true, 
            updateInfo: event.data 
          }));
          setIsVisible(true);
          break;
      }
    };

    window.electronAPI.onUpdaterEvent(handleUpdaterEvent);

    return () => {
      // Cleanup listener if needed
    };
  }, []);

  const handleDownload = async () => {
    try {
      await window.electronAPI.updaterDownloadUpdate();
    } catch (error) {
      console.error('Failed to download update:', error);
    }
  };

  const handleInstall = async () => {
    try {
      await window.electronAPI.updaterQuitAndInstall();
    } catch (error) {
      console.error('Failed to install update:', error);
    }
  };

  const handleClose = () => {
    setIsVisible(false);
    if (onClose) {
      onClose();
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatSpeed = (bytesPerSecond: number): string => {
    return formatBytes(bytesPerSecond) + '/s';
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 max-w-md">
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
        {/* Header */}
        <div className="bg-blue-500 text-white px-4 py-3 flex items-center justify-between">
          <div className="flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
            </svg>
            <h3 className="font-semibold">
              {status.checking && 'Checking for Updates'}
              {status.available && !status.downloading && !status.downloaded && 'Update Available'}
              {status.downloading && 'Downloading Update'}
              {status.downloaded && 'Update Ready'}
              {status.error && 'Update Error'}
            </h3>
          </div>
          <button
            onClick={handleClose}
            className="text-white hover:text-gray-200 transition-colors"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          {status.checking && (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-3"></div>
              <span className="text-gray-600">Checking for updates...</span>
            </div>
          )}

          {status.error && (
            <div className="text-red-600">
              <p className="font-medium">Failed to check for updates</p>
              <p className="text-sm mt-1">{status.error}</p>
            </div>
          )}

          {status.available && !status.downloading && !status.downloaded && status.updateInfo && (
            <div>
              <p className="text-gray-800 mb-2">
                Version <strong>{status.updateInfo.version}</strong> is available!
              </p>
              {status.updateInfo.releaseNotes && (
                <div className="bg-gray-50 rounded p-3 mb-3 max-h-32 overflow-y-auto">
                  <p className="text-sm text-gray-600 whitespace-pre-wrap">
                    {status.updateInfo.releaseNotes}
                  </p>
                </div>
              )}
              <div className="flex space-x-2">
                <button
                  onClick={handleDownload}
                  className="flex-1 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
                >
                  Download Update
                </button>
                <button
                  onClick={handleClose}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Later
                </button>
              </div>
            </div>
          )}

          {status.downloading && status.progress && (
            <div>
              <p className="text-gray-800 mb-2">Downloading update...</p>
              <div className="mb-2">
                <div className="flex justify-between text-sm text-gray-600 mb-1">
                  <span>{Math.round(status.progress.percent)}%</span>
                  <span>{formatSpeed(status.progress.bytesPerSecond)}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${status.progress.percent}%` }}
                  ></div>
                </div>
              </div>
              <p className="text-xs text-gray-500">
                {formatBytes(status.progress.transferred)} of {formatBytes(status.progress.total)}
              </p>
            </div>
          )}

          {status.downloaded && status.updateInfo && (
            <div>
              <p className="text-gray-800 mb-2">
                Update <strong>{status.updateInfo.version}</strong> is ready to install!
              </p>
              <p className="text-sm text-gray-600 mb-3">
                The application will restart to complete the installation.
              </p>
              <div className="flex space-x-2">
                <button
                  onClick={handleInstall}
                  className="flex-1 bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors"
                >
                  Restart & Install
                </button>
                <button
                  onClick={handleClose}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Later
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UpdateNotification;
