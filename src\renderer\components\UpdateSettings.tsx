import React, { useState, useEffect } from 'react';

interface UpdateConfig {
  enabled: boolean;
  checkOnStartup: boolean;
  checkInterval: number;
  allowPrerelease: boolean;
  autoDownload: boolean;
  autoInstallOnAppQuit: boolean;
  notifyUser: boolean;
}

interface UpdateSettingsProps {
  onConfigChange?: (config: UpdateConfig) => void;
}

const UpdateSettings: React.FC<UpdateSettingsProps> = ({ onConfigChange }) => {
  const [config, setConfig] = useState<UpdateConfig>({
    enabled: true,
    checkOnStartup: true,
    checkInterval: 24,
    allowPrerelease: false,
    autoDownload: true,
    autoInstallOnAppQuit: true,
    notifyUser: true
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      const result = await window.electronAPI.updaterGetConfig();
      if (result.success) {
        setConfig(result.config);
      }
    } catch (error) {
      console.error('Failed to load updater config:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveConfig = async (newConfig: UpdateConfig) => {
    setSaving(true);
    try {
      const result = await window.electronAPI.updaterUpdateConfig(newConfig);
      if (result.success) {
        setConfig(newConfig);
        if (onConfigChange) {
          onConfigChange(newConfig);
        }
      }
    } catch (error) {
      console.error('Failed to save updater config:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleToggle = (key: keyof UpdateConfig) => {
    const newConfig = { ...config, [key]: !config[key] };
    saveConfig(newConfig);
  };

  const handleIntervalChange = (interval: number) => {
    const newConfig = { ...config, checkInterval: interval };
    saveConfig(newConfig);
  };

  const handleCheckForUpdates = async () => {
    try {
      await window.electronAPI.updaterCheckForUpdates();
    } catch (error) {
      console.error('Failed to check for updates:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Auto-Update Settings</h3>
            <p className="text-sm text-gray-600 mt-1">
              Configure how the application handles updates
            </p>
          </div>
          <button
            onClick={handleCheckForUpdates}
            className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center"
          >
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
            </svg>
            Check Now
          </button>
        </div>

        <div className="space-y-4">
          {/* Enable Auto-Updates */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">
                Enable Auto-Updates
              </label>
              <p className="text-xs text-gray-500">
                Allow the application to automatically check for and install updates
              </p>
            </div>
            <button
              onClick={() => handleToggle('enabled')}
              disabled={saving}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                config.enabled ? 'bg-blue-500' : 'bg-gray-300'
              } ${saving ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  config.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>

          {config.enabled && (
            <>
              {/* Check on Startup */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Check on Startup
                  </label>
                  <p className="text-xs text-gray-500">
                    Check for updates when the application starts
                  </p>
                </div>
                <button
                  onClick={() => handleToggle('checkOnStartup')}
                  disabled={saving}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    config.checkOnStartup ? 'bg-blue-500' : 'bg-gray-300'
                  } ${saving ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      config.checkOnStartup ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* Check Interval */}
              <div>
                <label className="text-sm font-medium text-gray-700 block mb-2">
                  Check Interval
                </label>
                <select
                  value={config.checkInterval}
                  onChange={(e) => handleIntervalChange(Number(e.target.value))}
                  disabled={saving}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={1}>Every hour</option>
                  <option value={6}>Every 6 hours</option>
                  <option value={12}>Every 12 hours</option>
                  <option value={24}>Daily</option>
                  <option value={168}>Weekly</option>
                  <option value={0}>Never (manual only)</option>
                </select>
              </div>

              {/* Auto Download */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Auto Download
                  </label>
                  <p className="text-xs text-gray-500">
                    Automatically download updates when available
                  </p>
                </div>
                <button
                  onClick={() => handleToggle('autoDownload')}
                  disabled={saving}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    config.autoDownload ? 'bg-blue-500' : 'bg-gray-300'
                  } ${saving ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      config.autoDownload ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* Auto Install on Quit */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Auto Install on Quit
                  </label>
                  <p className="text-xs text-gray-500">
                    Install updates automatically when the application closes
                  </p>
                </div>
                <button
                  onClick={() => handleToggle('autoInstallOnAppQuit')}
                  disabled={saving}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    config.autoInstallOnAppQuit ? 'bg-blue-500' : 'bg-gray-300'
                  } ${saving ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      config.autoInstallOnAppQuit ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* Show Notifications */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Show Notifications
                  </label>
                  <p className="text-xs text-gray-500">
                    Show update notifications and dialogs
                  </p>
                </div>
                <button
                  onClick={() => handleToggle('notifyUser')}
                  disabled={saving}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    config.notifyUser ? 'bg-blue-500' : 'bg-gray-300'
                  } ${saving ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      config.notifyUser ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* Allow Prerelease */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">
                    Include Beta Versions
                  </label>
                  <p className="text-xs text-gray-500">
                    Include pre-release and beta versions in updates
                  </p>
                </div>
                <button
                  onClick={() => handleToggle('allowPrerelease')}
                  disabled={saving}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    config.allowPrerelease ? 'bg-blue-500' : 'bg-gray-300'
                  } ${saving ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      config.allowPrerelease ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </>
          )}
        </div>

        {saving && (
          <div className="mt-4 flex items-center text-sm text-gray-600">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
            Saving settings...
          </div>
        )}
      </div>
    </div>
  );
};

export default UpdateSettings;
