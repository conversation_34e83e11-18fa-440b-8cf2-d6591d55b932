import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { MenuItem } from '../types';
import ImageResolver from './ImageResolver';
import Icon from './Icon';

interface VirtualizedMenuGridProps {
  items: MenuItem[];
  onItemClick: (item: MenuItem) => void;
  itemHeight?: number;
  itemWidth?: number;
  gap?: number;
}

const VirtualizedMenuGrid: React.FC<VirtualizedMenuGridProps> = React.memo(
  ({ items, onItemClick, itemHeight = 200, itemWidth = 180, gap = 16 }) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
    const [scrollTop, setScrollTop] = useState(0);

    // Update container size on resize
    useEffect(() => {
      const updateSize = () => {
        if (containerRef.current) {
          const rect = containerRef.current.getBoundingClientRect();
          setContainerSize({ width: rect.width, height: rect.height });
        }
      };

      updateSize();
      window.addEventListener('resize', updateSize);
      return () => window.removeEventListener('resize', updateSize);
    }, []);

    // Calculate grid dimensions
    const gridMetrics = useMemo(() => {
      if (containerSize.width === 0) return { columns: 0, rows: 0, totalHeight: 0 };

      const availableWidth = containerSize.width - gap;
      const columns = Math.max(1, Math.floor(availableWidth / (itemWidth + gap)));
      const rows = Math.ceil(items.length / columns);
      const totalHeight = rows * (itemHeight + gap) - gap;

      return { columns, rows, totalHeight };
    }, [containerSize.width, items.length, itemWidth, itemHeight, gap]);

    // Calculate visible range
    const visibleRange = useMemo(() => {
      if (gridMetrics.columns === 0) return { start: 0, end: 0 };

      const rowHeight = itemHeight + gap;
      const startRow = Math.max(0, Math.floor(scrollTop / rowHeight) - 1);
      const endRow = Math.min(
        gridMetrics.rows - 1,
        Math.ceil((scrollTop + containerSize.height) / rowHeight) + 1
      );

      const start = startRow * gridMetrics.columns;
      const end = Math.min(items.length - 1, (endRow + 1) * gridMetrics.columns - 1);

      return { start, end };
    }, [scrollTop, containerSize.height, gridMetrics, itemHeight, gap, items.length]);

    // Get visible items
    const visibleItems = useMemo(() => {
      return items.slice(visibleRange.start, visibleRange.end + 1).map((item, index) => {
        const absoluteIndex = visibleRange.start + index;
        const row = Math.floor(absoluteIndex / gridMetrics.columns);
        const col = absoluteIndex % gridMetrics.columns;

        return {
          item,
          style: {
            position: 'absolute' as const,
            top: row * (itemHeight + gap),
            left: col * (itemWidth + gap),
            width: itemWidth,
            height: itemHeight,
          },
        };
      });
    }, [items, visibleRange, gridMetrics.columns, itemHeight, itemWidth, gap]);

    const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
      setScrollTop(e.currentTarget.scrollTop);
    }, []);

    if (items.length === 0) {
      return (
        <div className="empty-menu">
          <Icon name="utensils" size="xl" />
          <h3>No items found</h3>
          <p>Try adjusting your search or category filter</p>
        </div>
      );
    }

    return (
      <div
        ref={containerRef}
        className="virtualized-menu-grid"
        onScroll={handleScroll}
        style={{
          height: '100%',
          overflow: 'auto',
          position: 'relative',
        }}
      >
        <div
          style={{
            height: gridMetrics.totalHeight,
            position: 'relative',
          }}
        >
          {visibleItems.map(({ item, style }, index) => (
            <div
              key={item.id}
              className="menu-item-card virtualized"
              style={style}
              onClick={() => onItemClick(item)}
            >
              <div className="item-image">
                <ImageResolver src={item.image} alt={item.name} className="menu-image" />
                <div className="add-overlay">
                  <Icon name="plus" size="lg" />
                </div>
              </div>
              <div className="item-info">
                <h4 className="item-name">{item.name}</h4>
                <p className="item-price">₹{item.price.toFixed(2)}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }
);

export default VirtualizedMenuGrid;
