import React from 'react';
import { Table } from '../types';
import Icon from './Icon';

interface VisualTableSelectorProps {
  tables: Table[];
  selectedTable: Table | null;
  onTableSelect: (table: Table) => void;
  onClose: () => void;
}

const VisualTableSelector: React.FC<VisualTableSelectorProps> = React.memo(
  ({ tables, selectedTable, onTableSelect, onClose }) => {
    const getTableStatusIcon = (status: string) => {
      switch (status) {
        case 'available':
          return 'available';
        case 'occupied':
          return 'occupied';
        case 'reserved':
          return 'reserved';
        case 'maintenance':
          return 'maintenance';
        default:
          return 'utensils';
      }
    };

    const getTableStatusColor = (status: string) => {
      switch (status) {
        case 'available':
          return '#10b981'; // green
        case 'occupied':
          return '#ef4444'; // red
        case 'reserved':
          return '#f59e0b'; // yellow
        case 'maintenance':
          return '#6b7280'; // gray
        default:
          return '#6b7280';
      }
    };

    // Group tables by area/location
    const groupedTables = tables.reduce(
      (groups, table) => {
        const area = table.location || 'Main Area';
        if (!groups[area]) {
          groups[area] = [];
        }
        groups[area].push(table);
        return groups;
      },
      {} as Record<string, Table[]>
    );

    return (
      <div className="visual-table-selector-overlay">
        <div className="visual-table-selector">
          <div className="selector-header">
            <h3>Select Table</h3>
            <button className="close-btn" onClick={onClose}>
              <Icon name="cancel" size="sm" />
            </button>
          </div>

          <div className="table-legend">
            <div className="legend-item">
              <div className="legend-dot available"></div>
              <span>Available</span>
            </div>
            <div className="legend-item">
              <div className="legend-dot occupied"></div>
              <span>Occupied</span>
            </div>
            <div className="legend-item">
              <div className="legend-dot reserved"></div>
              <span>Reserved</span>
            </div>
            <div className="legend-item">
              <div className="legend-dot maintenance"></div>
              <span>Maintenance</span>
            </div>
          </div>

          <div className="table-areas">
            {Object.entries(groupedTables).map(([area, areaTables]) => (
              <div key={area} className="table-area">
                <h4 className="area-title">{area}</h4>
                <div className="table-grid">
                  {areaTables.map(table => (
                    <div
                      key={table.id}
                      className={`table-card ${table.status} ${
                        selectedTable?.id === table.id ? 'selected' : ''
                      } ${table.status === 'available' ? 'selectable' : 'disabled'}`}
                      onClick={() => {
                        if (table.status === 'available') {
                          onTableSelect(table);
                        }
                      }}
                    >
                      <div className="table-number">
                        <Icon name={getTableStatusIcon(table.status)} size="sm" />
                        <span>{table.tableNumber}</span>
                      </div>
                      <div className="table-info">
                        <span className="capacity">{table.capacity} pax</span>
                        <span className={`status ${table.status}`}>
                          {table.status.charAt(0).toUpperCase() + table.status.slice(1)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {selectedTable && (
            <div className="selected-table-info">
              <div className="selected-details">
                <Icon name="utensils" size="sm" />
                <span>Table {selectedTable.tableNumber}</span>
                <span>({selectedTable.capacity} pax)</span>
                {selectedTable.location && <span>• {selectedTable.location}</span>}
              </div>
              <button className="confirm-btn" onClick={onClose}>
                <Icon name="check-circle" size="sm" />
                Confirm Selection
              </button>
            </div>
          )}
        </div>
      </div>
    );
  }
);

export default VisualTableSelector;
