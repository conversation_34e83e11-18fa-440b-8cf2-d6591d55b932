import React, { useEffect, useState } from 'react';
import { useLicensing } from '../../hooks/useLicensing';
import SubscriptionPlans from './SubscriptionPlans';
import PaymentForm from './PaymentForm';
import { SubscriptionPlan } from '../../types';
import './LicenseValidator.css';

interface LicenseValidatorProps {
  userId: string;
  children: React.ReactNode;
}

const LicenseValidator: React.FC<LicenseValidatorProps> = ({ userId, children }) => {
  const { subscriptionStatus, isTrialExpired, isSubscriptionExpired, daysUntilExpiry } =
    useLicensing(userId);
  const [showExpiredModal, setShowExpiredModal] = useState(false);
  const [showExpiryWarning, setShowExpiryWarning] = useState(false);
  const [showPlans, setShowPlans] = useState(false);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);

  useEffect(() => {
    if (isTrialExpired || isSubscriptionExpired) {
      setShowExpiredModal(true);
    } else if (daysUntilExpiry <= 3 && daysUntilExpiry > 0) {
      setShowExpiryWarning(true);
    }
  }, [isTrialExpired, isSubscriptionExpired, daysUntilExpiry]);

  const handlePlanSelect = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setShowPaymentForm(true);
    setShowPlans(false);
  };

  const handlePaymentSuccess = async () => {
    setShowPaymentForm(false);
    setSelectedPlan(null);
    setShowExpiredModal(false);
    setShowExpiryWarning(false);

    // Show success message
    alert('Payment successful! Your subscription has been renewed.');
  };

  const handlePaymentCancel = () => {
    setShowPaymentForm(false);
    setSelectedPlan(null);
  };

  const handleDismissWarning = () => {
    setShowExpiryWarning(false);
  };

  // If subscription is expired, show modal and block access
  if ((isTrialExpired || isSubscriptionExpired) && showExpiredModal) {
    return (
      <div className="license-validator">
        <div className="expired-overlay">
          <div className="expired-modal">
            <div className="expired-content">
              <div className="expired-icon">⚠️</div>
              <h2>Subscription Expired</h2>
              <p>
                {isTrialExpired
                  ? 'Your free trial has expired. Please upgrade to continue using Zyka POS.'
                  : 'Your subscription has expired. Please renew to continue using Zyka POS.'}
              </p>

              <div className="expired-actions">
                <button className="btn btn-primary" onClick={() => setShowPlans(true)}>
                  {isTrialExpired ? 'Upgrade Now' : 'Renew Subscription'}
                </button>
              </div>

              <div className="limited-access-note">
                <p>
                  <strong>Limited Access:</strong> You can still view your data, but creating new
                  orders and accessing premium features is disabled.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Show limited version of the app */}
        <div className="limited-access-overlay">{children}</div>

        {/* Modals */}
        {showPlans && (
          <div className="modal-overlay">
            <div className="modal-content">
              <div className="modal-header">
                <h2>{isTrialExpired ? 'Choose Your Plan' : 'Renew Your Subscription'}</h2>
                <button className="close-btn" onClick={() => setShowPlans(false)}>
                  ×
                </button>
              </div>
              <SubscriptionPlans
                userId={userId}
                currentStatus={subscriptionStatus || undefined}
                onPlanSelect={handlePlanSelect}
                showCurrentPlan={false}
              />
            </div>
          </div>
        )}

        {showPaymentForm && selectedPlan && (
          <PaymentForm
            plan={selectedPlan}
            userId={userId}
            onPaymentSuccess={handlePaymentSuccess}
            onCancel={handlePaymentCancel}
          />
        )}
      </div>
    );
  }

  return (
    <div className="license-validator">
      {children}

      {/* Expiry Warning Banner */}
      {showExpiryWarning && (
        <div className="expiry-warning-banner">
          <div className="warning-content">
            <div className="warning-icon">⏰</div>
            <div className="warning-text">
              <strong>Subscription Expiring Soon!</strong>
              <span>
                Your subscription expires in {daysUntilExpiry} days. Renew now to avoid service
                interruption.
              </span>
            </div>
            <div className="warning-actions">
              <button className="btn btn-primary btn-sm" onClick={() => setShowPlans(true)}>
                Renew Now
              </button>
              <button className="btn btn-ghost btn-sm" onClick={handleDismissWarning}>
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modals */}
      {showPlans && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Renew Your Subscription</h2>
              <button className="close-btn" onClick={() => setShowPlans(false)}>
                ×
              </button>
            </div>
            <SubscriptionPlans
              userId={userId}
              currentStatus={subscriptionStatus || undefined}
              onPlanSelect={handlePlanSelect}
              showCurrentPlan={false}
            />
          </div>
        </div>
      )}

      {showPaymentForm && selectedPlan && (
        <PaymentForm
          plan={selectedPlan}
          userId={userId}
          onPaymentSuccess={handlePaymentSuccess}
          onCancel={handlePaymentCancel}
        />
      )}
    </div>
  );
};

export default LicenseValidator;
