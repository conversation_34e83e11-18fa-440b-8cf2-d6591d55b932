.notification-bell-container {
  position: relative;
  display: inline-block;
}

.notification-bell {
  position: relative;
  background: transparent;
  border: none;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-bell:hover {
  background: rgba(0, 0, 0, 0.05);
}

.notification-bell.active {
  background: rgba(59, 130, 246, 0.1);
}

.notification-bell.has-unread .bell-icon {
  animation: bellRing 2s ease-in-out infinite;
}

.bell-icon {
  font-size: 18px;
  color: #6b7280;
  transition: color 0.2s ease;
}

.notification-bell:hover .bell-icon,
.notification-bell.active .bell-icon {
  color: #3b82f6;
}

.notification-badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background: #ef4444;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 5px;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 380px;
  max-height: 500px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  animation: dropdownSlide 0.2s ease-out;
}

.notification-header {
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f9fafb;
}

.notification-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.notification-header-actions {
  display: flex;
  gap: 8px;
}

.header-action-btn {
  background: transparent;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.header-action-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
  color: #374151;
}

.notification-filters {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  gap: 8px;
  overflow-x: auto;
}

.filter-btn {
  background: transparent;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;
}

.filter-btn:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.filter-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.notification-list {
  max-height: 320px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  gap: 12px;
  position: relative;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.notification-item:hover {
  background: #f9fafb;
}

.notification-item.unread {
  background: #eff6ff;
}

.notification-item.unread:hover {
  background: #dbeafe;
}

.notification-item.priority-urgent {
  border-left: 3px solid #ef4444;
}

.notification-item.priority-high {
  border-left: 3px solid #f59e0b;
}

.notification-item.priority-low {
  border-left: 3px solid #10b981;
}

.notification-icon {
  font-size: 16px;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 4px;
}

.notification-title {
  font-weight: 600;
  font-size: 13px;
  color: #1f2937;
  line-height: 1.4;
}

.notification-time {
  font-size: 11px;
  color: #9ca3af;
  flex-shrink: 0;
  margin-left: 8px;
}

.notification-message {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
  margin-bottom: 8px;
  word-wrap: break-word;
}

.notification-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions {
  opacity: 1;
}

.notification-action-btn {
  background: transparent;
  border: none;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 10px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.notification-action-btn:hover {
  background: rgba(0, 0, 0, 0.1);
}

.notification-action-btn.mark-read {
  color: #10b981;
}

.notification-action-btn.delete {
  color: #ef4444;
}

.unread-indicator {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: #3b82f6;
  border-radius: 50%;
}

.notification-loading {
  padding: 40px 16px;
  text-align: center;
  color: #6b7280;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 12px;
}

.notification-empty {
  padding: 40px 16px;
  text-align: center;
  color: #9ca3af;
}

.empty-icon {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

.notification-empty p {
  margin: 0 0 4px 0;
  font-weight: 500;
  color: #6b7280;
}

.empty-subtitle {
  font-size: 12px;
  color: #9ca3af;
}

.notification-footer {
  padding: 12px 16px;
  border-top: 1px solid #f3f4f6;
  background: #f9fafb;
  text-align: center;
}

.notification-count {
  font-size: 11px;
  color: #9ca3af;
}

/* Animations */
@keyframes dropdownSlide {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bellRing {
  0%,
  50%,
  100% {
    transform: rotate(0deg);
  }
  10%,
  30% {
    transform: rotate(-10deg);
  }
  20%,
  40% {
    transform: rotate(10deg);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 640px) {
  .notification-dropdown {
    width: 320px;
    right: -20px;
  }

  .notification-filters {
    padding: 8px 12px;
  }

  .filter-btn {
    padding: 3px 8px;
    font-size: 11px;
  }

  .notification-item {
    padding: 10px 12px;
  }

  .notification-header,
  .notification-footer {
    padding: 12px;
  }
}
