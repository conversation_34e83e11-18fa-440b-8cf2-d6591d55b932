import React, { useState, useRef, useEffect } from 'react';
import { useNotifications } from '../../contexts/NotificationContext';
import { Notification, NotificationType } from '../../types';
import Icon from '../Icon';
import './NotificationBell.css';

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onMarkAsRead,
  onDelete,
}) => {
  const getTypeIcon = (type: NotificationType) => {
    switch (type) {
      case 'orders':
        return <Icon name="utensils" size="sm" />;
      case 'tables':
        return <Icon name="table" size="sm" />;
      case 'bills':
        return <Icon name="payment" size="sm" />;
      case 'menu':
        return <Icon name="clipboard" size="sm" />;
      case 'tax':
        return <Icon name="receipt" size="sm" />;
      case 'system':
        return <Icon name="gear" size="sm" />;
      default:
        return <Icon name="megaphone" size="sm" />;
    }
  };

  const getPriorityClass = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'priority-urgent';
      case 'high':
        return 'priority-high';
      case 'low':
        return 'priority-low';
      default:
        return 'priority-normal';
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    onMarkAsRead(notification.id);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(notification.id);
  };

  return (
    <div
      className={`notification-item ${!notification.readStatus ? 'unread' : ''} ${getPriorityClass(notification.priority)}`}
    >
      <div className="notification-icon">{getTypeIcon(notification.type)}</div>
      <div className="notification-content">
        <div className="notification-header">
          <span className="notification-title">{notification.title}</span>
          <span className="notification-time">{formatTime(notification.createdAt)}</span>
        </div>
        <div className="notification-message">{notification.message}</div>
        <div className="notification-actions">
          {!notification.readStatus && (
            <button
              className="notification-action-btn mark-read"
              onClick={handleMarkAsRead}
              title="Mark as read"
            >
              <Icon name="check-circle" size="xs" />
            </button>
          )}
          <button
            className="notification-action-btn delete"
            onClick={handleDelete}
            title="Delete notification"
          >
            <Icon name="trash" size="xs" />
          </button>
        </div>
      </div>
      {!notification.readStatus && <div className="unread-indicator"></div>}
    </div>
  );
};

interface NotificationBellProps {
  userId: string;
}

const NotificationBell: React.FC<NotificationBellProps> = ({ userId }) => {
  const {
    state,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAllNotifications,
    loadNotifications,
  } = useNotifications();
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState<NotificationType | 'all'>('all');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const bellRef = useRef<HTMLButtonElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        bellRef.current &&
        !bellRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleBellClick = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      // Refresh notifications when opening
      loadNotifications(userId);
    }
  };

  const handleMarkAllAsRead = () => {
    markAllAsRead(userId);
  };

  const handleClearAll = () => {
    if (window.confirm('Are you sure you want to clear all notifications?')) {
      clearAllNotifications(userId);
    }
  };

  const filteredNotifications =
    filter === 'all' ? state.notifications : state.notifications.filter(n => n.type === filter);

  const unreadCount = state.unreadCount;
  const hasUnread = unreadCount > 0;

  return (
    <div className="notification-bell-container">
      <button
        ref={bellRef}
        className={`notification-bell ${hasUnread ? 'has-unread' : ''} ${isOpen ? 'active' : ''}`}
        onClick={handleBellClick}
        title={`${unreadCount} unread notifications`}
      >
        <Icon name="bell" size="sm" />
        {hasUnread && (
          <span className="notification-badge">{unreadCount > 99 ? '99+' : unreadCount}</span>
        )}
      </button>

      {isOpen && (
        <div ref={dropdownRef} className="notification-dropdown">
          <div className="notification-header">
            <h3>Notifications</h3>
            <div className="notification-header-actions">
              {hasUnread && (
                <button
                  className="header-action-btn"
                  onClick={handleMarkAllAsRead}
                  title="Mark all as read"
                >
                  <Icon name="check-circle" size="xs" /> All
                </button>
              )}
              <button
                className="header-action-btn"
                onClick={handleClearAll}
                title="Clear all notifications"
              >
                <Icon name="trash" size="xs" /> Clear
              </button>
            </div>
          </div>

          <div className="notification-filters">
            <button
              className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
              onClick={() => setFilter('all')}
            >
              All
            </button>
            <button
              className={`filter-btn ${filter === 'orders' ? 'active' : ''}`}
              onClick={() => setFilter('orders')}
            >
              Orders
            </button>
            <button
              className={`filter-btn ${filter === 'tables' ? 'active' : ''}`}
              onClick={() => setFilter('tables')}
            >
              Tables
            </button>
            <button
              className={`filter-btn ${filter === 'bills' ? 'active' : ''}`}
              onClick={() => setFilter('bills')}
            >
              Bills
            </button>
            <button
              className={`filter-btn ${filter === 'system' ? 'active' : ''}`}
              onClick={() => setFilter('system')}
            >
              System
            </button>
          </div>

          <div className="notification-list">
            {state.isLoading ? (
              <div className="notification-loading">
                <div className="loading-spinner"></div>
                <span>Loading notifications...</span>
              </div>
            ) : filteredNotifications.length === 0 ? (
              <div className="notification-empty">
                <span className="empty-icon">
                  <Icon name="bell-off" size="xl" />
                </span>
                <p>No notifications</p>
                <span className="empty-subtitle">
                  {filter === 'all' ? "You're all caught up!" : `No ${filter} notifications`}
                </span>
              </div>
            ) : (
              filteredNotifications.map(notification => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  onMarkAsRead={markAsRead}
                  onDelete={deleteNotification}
                />
              ))
            )}
          </div>

          {filteredNotifications.length > 0 && (
            <div className="notification-footer">
              <span className="notification-count">
                {filteredNotifications.length} notification
                {filteredNotifications.length !== 1 ? 's' : ''}
                {filter !== 'all' && ` in ${filter}`}
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default NotificationBell;
