.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
  pointer-events: none;
}

.toast {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-left: 4px solid;
  min-height: 64px;
  display: flex;
  align-items: center;
  padding: 0;
  pointer-events: auto;
  animation: slideInRight 0.3s ease-out;
  transition: all 0.3s ease;
}

.toast:hover {
  transform: translateX(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.toast-success {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border-left-color: #10b981;
  color: #065f46;
}

.toast-success .toast-title {
  color: #064e3b;
  font-weight: 700;
}

.toast-success .toast-text {
  color: #065f46;
}

.toast-warning {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-left-color: #f59e0b;
  color: #92400e;
}

.toast-warning .toast-title {
  color: #78350f;
  font-weight: 700;
}

.toast-warning .toast-text {
  color: #92400e;
}

.toast-error {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border-left-color: #ef4444;
  color: #991b1b;
}

.toast-error .toast-title {
  color: #7f1d1d;
  font-weight: 700;
}

.toast-error .toast-text {
  color: #991b1b;
}

.toast-info {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-left-color: #3b82f6;
  color: #1e40af;
}

.toast-info .toast-title {
  color: #1e3a8a;
  font-weight: 700;
}

.toast-info .toast-text {
  color: #1e40af;
}

.toast-content {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 16px;
  gap: 12px;
}

.toast-icon {
  font-size: 20px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.toast-message {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 2px;
  line-height: 1.4;
}

.toast-text {
  font-size: 13px;
  line-height: 1.4;
  word-wrap: break-word;
}

.toast-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.toast-action-btn {
  background: transparent;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toast-action-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.toast-close-btn {
  background: transparent;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

/* Close button colors for each toast type */
.toast-success .toast-close-btn {
  color: #065f46;
}

.toast-success .toast-close-btn:hover {
  background: rgba(6, 95, 70, 0.1);
  color: #064e3b;
}

.toast-warning .toast-close-btn {
  color: #92400e;
}

.toast-warning .toast-close-btn:hover {
  background: rgba(146, 64, 14, 0.1);
  color: #78350f;
}

.toast-error .toast-close-btn {
  color: #991b1b;
}

.toast-error .toast-close-btn:hover {
  background: rgba(153, 27, 27, 0.1);
  color: #7f1d1d;
}

.toast-info .toast-close-btn {
  color: #1e40af;
}

.toast-info .toast-close-btn:hover {
  background: rgba(30, 64, 175, 0.1);
  color: #1e3a8a;
}

/* Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.toast.removing {
  animation: slideOutRight 0.3s ease-in forwards;
}

/* Responsive design */
@media (max-width: 640px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }

  .toast {
    margin: 0;
  }

  .toast-content {
    padding: 12px;
  }

  .toast-title {
    font-size: 13px;
  }

  .toast-text {
    font-size: 12px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .toast {
    background: #1f2937;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .toast-title {
    color: #f9fafb;
  }

  .toast-text {
    color: #d1d5db;
  }

  .toast-action-btn {
    border-color: #4b5563;
    color: #d1d5db;
  }

  .toast-action-btn:hover {
    background: #374151;
    border-color: #6b7280;
  }

  .toast-close-btn {
    color: #9ca3af;
  }

  .toast-close-btn:hover {
    background: #374151;
    color: #d1d5db;
  }
}
