import React, { useState, useEffect } from 'react';
import { AppSettings } from '../../types';

interface GeneralSettingsProps {
  restaurantId: string;
}

const GeneralSettings: React.FC<GeneralSettingsProps> = ({ restaurantId }) => {
  const [settings, setSettings] = useState<AppSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  const currencies = [
    { code: 'INR', symbol: '₹', name: 'Indian Rupee' },
    { code: 'USD', symbol: '$', name: 'US Dollar' },
    { code: 'EUR', symbol: '€', name: 'Euro' },
    { code: 'GBP', symbol: '£', name: 'British Pound' },
  ];

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'hi', name: 'Hindi' },
    { code: 'es', name: 'Spanish' },
    { code: 'fr', name: 'French' },
  ];

  const timezones = [
    'Asia/Kolkata',
    'America/New_York',
    'Europe/London',
    'Asia/Tokyo',
    'Australia/Sydney',
  ];

  const dateFormats = ['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD', 'DD-MM-YYYY'];

  useEffect(() => {
    loadSettings();
  }, [restaurantId]);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      const data = await window.electronAPI.getAppSettings(restaurantId);

      if (data) {
        setSettings(data);
      } else {
        // Set default settings
        setSettings({
          id: '',
          restaurantId,
          general: {
            currency: 'INR',
            currencySymbol: '₹',
            language: 'en',
            timezone: 'Asia/Kolkata',
            dateFormat: 'DD/MM/YYYY',
            timeFormat: '12h',
          },
          pos: {
            autoSaveOrders: true,
            soundEnabled: true,
            showItemImages: true,
            defaultTaxRate: undefined,
          },
          updatedAt: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!settings) return;

    try {
      setIsSaving(true);
      const result = await window.electronAPI.saveAppSettings(settings);

      if (result.success) {
        setSettings(result.settings!);
        alert('Settings saved successfully!');
      } else {
        alert('Failed to save settings: ' + result.error);
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Failed to save settings');
    } finally {
      setIsSaving(false);
    }
  };

  const updateSetting = (path: string, value: any) => {
    if (!settings) return;

    const pathArray = path.split('.');
    const newSettings = { ...settings };
    let current: any = newSettings;

    for (let i = 0; i < pathArray.length - 1; i++) {
      current = current[pathArray[i]];
    }
    current[pathArray[pathArray.length - 1]] = value;

    setSettings(newSettings);
  };

  if (isLoading) {
    return (
      <div className="settings-loading">
        <div className="loading-spinner"></div>
        <p>Loading settings...</p>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="settings-error">
        <p>Failed to load settings</p>
        <button onClick={loadSettings} className="btn btn-primary">
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="general-settings">
      <div className="settings-section">
        <h3 className="section-title">Regional Settings</h3>

        <div className="form-grid">
          <div className="form-group">
            <label className="form-label">Currency</label>
            <select
              className="form-select"
              value={settings.general.currency}
              onChange={e => {
                const currency = currencies.find(c => c.code === e.target.value);
                updateSetting('general.currency', e.target.value);
                updateSetting('general.currencySymbol', currency?.symbol || '₹');
              }}
            >
              {currencies.map(currency => (
                <option key={currency.code} value={currency.code}>
                  {currency.symbol} {currency.name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">Language</label>
            <select
              className="form-select"
              value={settings.general.language}
              onChange={e => updateSetting('general.language', e.target.value)}
            >
              {languages.map(lang => (
                <option key={lang.code} value={lang.code}>
                  {lang.name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">Timezone</label>
            <select
              className="form-select"
              value={settings.general.timezone}
              onChange={e => updateSetting('general.timezone', e.target.value)}
            >
              {timezones.map(tz => (
                <option key={tz} value={tz}>
                  {tz}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">Date Format</label>
            <select
              className="form-select"
              value={settings.general.dateFormat}
              onChange={e => updateSetting('general.dateFormat', e.target.value)}
            >
              {dateFormats.map(format => (
                <option key={format} value={format}>
                  {format}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">Time Format</label>
            <select
              className="form-select"
              value={settings.general.timeFormat}
              onChange={e => updateSetting('general.timeFormat', e.target.value)}
            >
              <option value="12h">12 Hour (AM/PM)</option>
              <option value="24h">24 Hour</option>
            </select>
          </div>
        </div>
      </div>

      <div className="settings-section">
        <h3 className="section-title">POS Settings</h3>

        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.pos.autoSaveOrders}
              onChange={e => updateSetting('pos.autoSaveOrders', e.target.checked)}
            />
            <span className="checkbox-text">Auto-save orders</span>
            <span className="checkbox-description">
              Automatically save orders as they are created
            </span>
          </label>
        </div>

        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.pos.soundEnabled}
              onChange={e => updateSetting('pos.soundEnabled', e.target.checked)}
            />
            <div className="checkbox-content">
              <span className="checkbox-text">Enable sounds</span>
              <span className="checkbox-description">
                Play sounds for notifications and actions
              </span>
            </div>
          </label>
        </div>

        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={settings.pos.showItemImages}
              onChange={e => updateSetting('pos.showItemImages', e.target.checked)}
            />
            <div className="checkbox-content">
              <span className="checkbox-text">Show item images</span>
              <span className="checkbox-description">Display images for menu items in POS</span>
            </div>
          </label>
        </div>
      </div>

      <div className="settings-actions">
        <button onClick={handleSave} disabled={isSaving} className="btn btn-primary">
          {isSaving ? 'Saving...' : 'Save Settings'}
        </button>
      </div>
    </div>
  );
};

export default GeneralSettings;
