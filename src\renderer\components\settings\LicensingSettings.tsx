import React, { useState, useEffect } from 'react';
import { SubscriptionStatus, SubscriptionPlan, PaymentTransaction } from '../../types';
import SubscriptionPlans from '../licensing/SubscriptionPlans';
import PaymentForm from '../licensing/PaymentForm';
import './LicensingSettings.css';

interface LicensingSettingsProps {
  userId: string;
}

const LicensingSettings: React.FC<LicensingSettingsProps> = ({ userId }) => {
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [paymentHistory, setPaymentHistory] = useState<PaymentTransaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [showPlans, setShowPlans] = useState(false);

  useEffect(() => {
    loadSubscriptionData();
  }, [userId]);

  const loadSubscriptionData = async () => {
    try {
      setIsLoading(true);
      const [status, history] = await Promise.all([
        window.electronAPI.getSubscriptionStatus(userId),
        window.electronAPI.getPaymentHistory(userId),
      ]);

      setSubscriptionStatus(status);
      setPaymentHistory(history);
    } catch (error) {
      console.error('Error loading subscription data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePlanSelect = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setShowPaymentForm(true);
    setShowPlans(false);
  };

  const handlePaymentSuccess = async (paymentData: any) => {
    setShowPaymentForm(false);
    setSelectedPlan(null);
    await loadSubscriptionData();

    // Show success message
    alert('Payment successful! Your subscription has been upgraded.');
  };

  const handlePaymentCancel = () => {
    setShowPaymentForm(false);
    setSelectedPlan(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number, currency: string = 'INR') => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'trial':
        return '#f59e0b';
      case 'active':
        return '#10b981';
      case 'expired':
        return '#dc2626';
      case 'cancelled':
        return '#6b7280';
      default:
        return '#6b7280';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return '#10b981';
      case 'pending':
        return '#f59e0b';
      case 'failed':
        return '#dc2626';
      case 'refunded':
        return '#6b7280';
      default:
        return '#6b7280';
    }
  };

  if (isLoading) {
    return (
      <div className="licensing-settings-loading">
        <div className="loading-spinner"></div>
        <p>Loading subscription information...</p>
      </div>
    );
  }

  return (
    <div className="licensing-settings">
      {/* Current Subscription Status */}
      <div className="subscription-overview">
        <div className="overview-header">
          <h3>Subscription Overview</h3>
          {subscriptionStatus && subscriptionStatus.status !== 'expired' && (
            <button className="btn btn-primary" onClick={() => setShowPlans(true)}>
              Upgrade Plan
            </button>
          )}
        </div>

        {subscriptionStatus && (
          <div className="status-cards">
            <div className="status-card">
              <div className="card-header">
                <h4>Current Plan</h4>
                <span
                  className="status-badge"
                  style={{ backgroundColor: getStatusColor(subscriptionStatus.status) }}
                >
                  {subscriptionStatus.status === 'trial'
                    ? 'Trial'
                    : subscriptionStatus.status === 'active'
                      ? 'Active'
                      : subscriptionStatus.status === 'expired'
                        ? 'Expired'
                        : 'Cancelled'}
                </span>
              </div>
              <div className="card-content">
                <p className="plan-name">
                  {subscriptionStatus.plan
                    ? `${subscriptionStatus.plan.charAt(0).toUpperCase()}${subscriptionStatus.plan.slice(1)} Plan`
                    : 'No active plan'}
                </p>
                {subscriptionStatus.isTrialActive && (
                  <p className="trial-info">🎉 You're currently on a free trial</p>
                )}
              </div>
            </div>

            <div className="status-card">
              <div className="card-header">
                <h4>Days Remaining</h4>
              </div>
              <div className="card-content">
                <p
                  className={`days-count ${subscriptionStatus.daysRemaining <= 3 ? 'warning' : ''}`}
                >
                  {subscriptionStatus.daysRemaining}
                </p>
                <p className="days-label">
                  {subscriptionStatus.daysRemaining === 1 ? 'day' : 'days'} left
                </p>
              </div>
            </div>

            {subscriptionStatus.nextBillingDate && (
              <div className="status-card">
                <div className="card-header">
                  <h4>Next Billing</h4>
                </div>
                <div className="card-content">
                  <p className="billing-date">{formatDate(subscriptionStatus.nextBillingDate)}</p>
                  <p className="auto-renew">
                    Auto-renew: {subscriptionStatus.autoRenew ? 'Enabled' : 'Disabled'}
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        {subscriptionStatus?.status === 'expired' && (
          <div className="expired-notice">
            <div className="notice-content">
              <h4>⚠️ Subscription Expired</h4>
              <p>Your subscription has expired. Upgrade now to continue using all features.</p>
              <button className="btn btn-primary" onClick={() => setShowPlans(true)}>
                Renew Subscription
              </button>
            </div>
          </div>
        )}

        {(subscriptionStatus?.daysRemaining || 0) <= 3 &&
          subscriptionStatus?.status !== 'expired' && (
            <div className="expiry-warning">
              <div className="warning-content">
                <h4>⏰ Subscription Expiring Soon</h4>
                <p>
                  Your subscription expires in {subscriptionStatus?.daysRemaining || 0} days. Renew
                  now to avoid service interruption.
                </p>
                <button className="btn btn-primary" onClick={() => setShowPlans(true)}>
                  Renew Now
                </button>
              </div>
            </div>
          )}
      </div>

      {/* Payment History */}
      <div className="payment-history">
        <h3>Payment History</h3>
        {paymentHistory.length > 0 ? (
          <div className="history-table">
            <div className="table-header">
              <div className="col">Date</div>
              <div className="col">Amount</div>
              <div className="col">Method</div>
              <div className="col">Status</div>
              <div className="col">Transaction ID</div>
            </div>
            {paymentHistory.map(transaction => (
              <div key={transaction.id} className="table-row">
                <div className="col">{formatDate(transaction.createdAt)}</div>
                <div className="col">
                  {formatCurrency(transaction.amount, transaction.currency)}
                </div>
                <div className="col">
                  <span className="payment-method">{transaction.paymentMethod.toUpperCase()}</span>
                </div>
                <div className="col">
                  <span
                    className="payment-status"
                    style={{ color: getPaymentStatusColor(transaction.status) }}
                  >
                    {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                  </span>
                </div>
                <div className="col">
                  <code className="transaction-id">{transaction.transactionId || 'N/A'}</code>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="no-history">
            <p>No payment history available</p>
          </div>
        )}
      </div>

      {/* Modals */}
      {showPlans && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Choose Your Plan</h2>
              <button className="close-btn" onClick={() => setShowPlans(false)}>
                ×
              </button>
            </div>
            <SubscriptionPlans
              userId={userId}
              currentStatus={subscriptionStatus || undefined}
              onPlanSelect={handlePlanSelect}
              showCurrentPlan={false}
            />
          </div>
        </div>
      )}

      {showPaymentForm && selectedPlan && (
        <PaymentForm
          plan={selectedPlan}
          userId={userId}
          onPaymentSuccess={handlePaymentSuccess}
          onCancel={handlePaymentCancel}
        />
      )}
    </div>
  );
};

export default LicensingSettings;
