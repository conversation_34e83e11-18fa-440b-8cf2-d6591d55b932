.notification-settings {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

.settings-header {
  margin-bottom: 32px;
}

.settings-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.settings-header p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.notification-settings-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.notification-categories {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.notification-category {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.notification-category:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.notification-category.disabled {
  opacity: 0.6;
}

.category-header {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f3f4f6;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.category-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 8px;
}

.category-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.category-description {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
}

.category-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #d1d5db;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #3b82f6;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

input:disabled + .toggle-slider {
  opacity: 0.5;
  cursor: not-allowed;
}

.saved-indicator {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
  animation: fadeIn 0.3s ease;
}

.category-options {
  padding: 20px;
  background: #f9fafb;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-group h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.option-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.option-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #4b5563;
  cursor: pointer;
  flex: 1;
}

.option-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
  cursor: pointer;
}

.option-label input[type="checkbox"]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.time-select,
.frequency-select {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  background: white;
  color: #374151;
  cursor: pointer;
  min-width: 120px;
}

.time-select:focus,
.frequency-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.time-select:disabled,
.frequency-select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f3f4f6;
}

.settings-footer {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.footer-info {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 16px;
}

.footer-info p {
  margin: 0;
  font-size: 13px;
  color: #0c4a6e;
  line-height: 1.5;
}

.footer-info strong {
  color: #075985;
}

/* Animations */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .notification-settings {
    padding: 16px;
  }

  .settings-header {
    margin-bottom: 24px;
  }

  .settings-header h2 {
    font-size: 20px;
  }

  .category-header {
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .category-info {
    gap: 12px;
  }

  .category-icon {
    width: 32px;
    height: 32px;
    font-size: 18px;
  }

  .category-title {
    font-size: 15px;
  }

  .category-description {
    font-size: 12px;
  }

  .category-options {
    padding: 16px;
    gap: 20px;
  }

  .option-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .option-label {
    width: 100%;
  }

  .time-select,
  .frequency-select {
    width: 100%;
    min-width: auto;
  }
}
