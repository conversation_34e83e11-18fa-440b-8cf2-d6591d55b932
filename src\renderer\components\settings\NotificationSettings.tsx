import React, { useState, useEffect } from 'react';
import { useNotifications } from '../../contexts/NotificationContext';
import { NotificationPreference, NotificationType, NotificationFrequency } from '../../types';
import './NotificationSettings.css';

interface NotificationSettingsProps {
  userId: string;
}

interface CategoryConfig {
  type: NotificationType;
  label: string;
  description: string;
  icon: string;
}

const categoryConfigs: CategoryConfig[] = [
  {
    type: 'orders',
    label: 'Orders',
    description: 'New orders, status changes, and order updates',
    icon: '🍽️',
  },
  {
    type: 'tables',
    label: 'Tables',
    description: 'Table status changes and reservations',
    icon: '🪑',
  },
  {
    type: 'bills',
    label: 'Bills & Payments',
    description: 'Payment received, bills printed, payment overdue',
    icon: '💰',
  },
  {
    type: 'menu',
    label: 'Menu',
    description: 'Menu updates, stock alerts, availability changes',
    icon: '📋',
  },
  {
    type: 'tax',
    label: 'Tax & Pricing',
    description: 'Tax rate changes and pricing updates',
    icon: '🧾',
  },
  {
    type: 'system',
    label: 'System',
    description: 'System notifications, updates, and errors',
    icon: '⚙️',
  },
];

const NotificationSettings: React.FC<NotificationSettingsProps> = ({ userId }) => {
  const { state, loadPreferences, updatePreference } = useNotifications();
  const [isSaving, setIsSaving] = useState(false);
  const [savedCategory, setSavedCategory] = useState<NotificationType | null>(null);

  useEffect(() => {
    loadPreferences(userId);
  }, [userId, loadPreferences]);

  const getPreference = (category: NotificationType): NotificationPreference | undefined => {
    return state.preferences.find(p => p.category === category);
  };

  const handleToggleEnabled = async (category: NotificationType, enabled: boolean) => {
    setIsSaving(true);
    try {
      await updatePreference(userId, category, { enabled });
      setSavedCategory(category);
      setTimeout(() => setSavedCategory(null), 2000);
    } catch (error) {
      console.error('Error updating notification preference:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleToggleFeature = async (
    category: NotificationType,
    feature: keyof NotificationPreference,
    value: boolean
  ) => {
    setIsSaving(true);
    try {
      await updatePreference(userId, category, { [feature]: value });
      setSavedCategory(category);
      setTimeout(() => setSavedCategory(null), 2000);
    } catch (error) {
      console.error('Error updating notification preference:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleUpdateTime = async (category: NotificationType, time: number) => {
    setIsSaving(true);
    try {
      await updatePreference(userId, category, { autoDismissTime: time });
      setSavedCategory(category);
      setTimeout(() => setSavedCategory(null), 2000);
    } catch (error) {
      console.error('Error updating notification preference:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleUpdateFrequency = async (
    category: NotificationType,
    frequency: NotificationFrequency
  ) => {
    setIsSaving(true);
    try {
      await updatePreference(userId, category, { frequency });
      setSavedCategory(category);
      setTimeout(() => setSavedCategory(null), 2000);
    } catch (error) {
      console.error('Error updating notification preference:', error);
    } finally {
      setIsSaving(false);
    }
  };

  if (state.isLoading) {
    return (
      <div className="notification-settings-loading">
        <div className="loading-spinner"></div>
        <p>Loading notification preferences...</p>
      </div>
    );
  }

  return (
    <div className="notification-settings">
      <div className="settings-header">
        <h2>Notification Preferences</h2>
        <p>Customize how and when you receive notifications for different types of events.</p>
      </div>

      <div className="notification-categories">
        {categoryConfigs.map(config => {
          const preference = getPreference(config.type);
          const isEnabled = preference?.enabled !== false;
          const isSaved = savedCategory === config.type;

          return (
            <div
              key={config.type}
              className={`notification-category ${!isEnabled ? 'disabled' : ''}`}
            >
              <div className="category-header">
                <div className="category-info">
                  <span className="category-icon">{config.icon}</span>
                  <div>
                    <h3 className="category-title">{config.label}</h3>
                    <p className="category-description">{config.description}</p>
                  </div>
                </div>
                <div className="category-toggle">
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={isEnabled}
                      onChange={e => handleToggleEnabled(config.type, e.target.checked)}
                      disabled={isSaving}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                  {isSaved && <span className="saved-indicator">✓ Saved</span>}
                </div>
              </div>

              {isEnabled && (
                <div className="category-options">
                  <div className="option-group">
                    <h4>Notification Types</h4>
                    <div className="option-row">
                      <label className="checkbox-label">
                        <input
                          type="checkbox"
                          checked={preference?.toastEnabled !== false}
                          onChange={e =>
                            handleToggleFeature(config.type, 'toastEnabled', e.target.checked)
                          }
                          disabled={isSaving}
                        />
                        <div className="checkbox-content">
                          <span className="checkbox-text">Toast notifications</span>
                        </div>
                      </label>
                    </div>
                    <div className="option-row">
                      <label className="checkbox-label">
                        <input
                          type="checkbox"
                          checked={preference?.soundEnabled !== false}
                          onChange={e =>
                            handleToggleFeature(config.type, 'soundEnabled', e.target.checked)
                          }
                          disabled={isSaving}
                        />
                        <div className="checkbox-content">
                          <span className="checkbox-text">Sound alerts</span>
                        </div>
                      </label>
                    </div>
                    <div className="option-row">
                      <label className="checkbox-label">
                        <input
                          type="checkbox"
                          checked={preference?.desktopEnabled !== false}
                          onChange={e =>
                            handleToggleFeature(config.type, 'desktopEnabled', e.target.checked)
                          }
                          disabled={isSaving}
                        />
                        <div className="checkbox-content">
                          <span className="checkbox-text">Desktop notifications</span>
                        </div>
                      </label>
                    </div>
                  </div>

                  <div className="option-group">
                    <h4>Auto-dismiss</h4>
                    <div className="option-row">
                      <label className="checkbox-label">
                        <input
                          type="checkbox"
                          checked={preference?.autoDismissEnabled !== false}
                          onChange={e =>
                            handleToggleFeature(config.type, 'autoDismissEnabled', e.target.checked)
                          }
                          disabled={isSaving}
                        />
                        <div className="checkbox-content">
                          <span className="checkbox-text">Auto-dismiss notifications</span>
                        </div>
                      </label>
                    </div>
                    {preference?.autoDismissEnabled !== false && (
                      <div className="option-row">
                        <label className="option-label">
                          <span>Dismiss after:</span>
                          <select
                            value={preference?.autoDismissTime || 5}
                            onChange={e => handleUpdateTime(config.type, parseInt(e.target.value))}
                            disabled={isSaving}
                            className="time-select"
                          >
                            <option value={3}>3 seconds</option>
                            <option value={5}>5 seconds</option>
                            <option value={10}>10 seconds</option>
                            <option value={15}>15 seconds</option>
                            <option value={30}>30 seconds</option>
                          </select>
                        </label>
                      </div>
                    )}
                  </div>

                  <div className="option-group">
                    <h4>Frequency</h4>
                    <div className="option-row">
                      <label className="option-label">
                        <span>Notification frequency:</span>
                        <select
                          value={preference?.frequency || 'immediate'}
                          onChange={e =>
                            handleUpdateFrequency(
                              config.type,
                              e.target.value as NotificationFrequency
                            )
                          }
                          disabled={isSaving}
                          className="frequency-select"
                        >
                          <option value="immediate">Immediate</option>
                          <option value="batched">Batched (every 5 minutes)</option>
                          <option value="daily">Daily summary</option>
                        </select>
                      </label>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      <div className="settings-footer">
        <div className="footer-info">
          <p>
            <strong>Note:</strong> Some notifications may still appear for critical system events
            regardless of these settings to ensure proper operation of your restaurant.
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotificationSettings;
