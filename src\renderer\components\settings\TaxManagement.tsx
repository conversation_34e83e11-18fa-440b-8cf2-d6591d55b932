import React, { useState, useEffect, useCallback } from 'react';
import { TaxRate } from '../../types';
import Icon from '../Icon';

interface TaxManagementProps {
  restaurantId: string;
}

interface TaxForm {
  name: string;
  rate: string;
  type: 'percentage' | 'fixed';
  isDefault: boolean;
  isActive: boolean;
}

interface ToastMessage {
  type: 'success' | 'error' | 'info' | 'warning';
  message: string;
  duration?: number;
}

const TaxManagement: React.FC<TaxManagementProps> = ({ restaurantId }) => {
  const [taxRates, setTaxRates] = useState<TaxRate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingTax, setEditingTax] = useState<TaxRate | null>(null);
  const [toast, setToast] = useState<ToastMessage | null>(null);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'percentage' | 'fixed'>('all');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');
  const [formData, setFormData] = useState<TaxForm>({
    name: '',
    rate: '',
    type: 'percentage',
    isDefault: false,
    isActive: true,
  });

  const commonTaxRates = [
    {
      name: 'GST 5%',
      rate: 5,
      type: 'percentage' as const,
      description: 'Goods and Services Tax - 5%',
      category: 'GST',
    },
    {
      name: 'GST 12%',
      rate: 12,
      type: 'percentage' as const,
      description: 'Goods and Services Tax - 12%',
      category: 'GST',
    },
    {
      name: 'GST 18%',
      rate: 18,
      type: 'percentage' as const,
      description: 'Goods and Services Tax - 18%',
      category: 'GST',
    },
    {
      name: 'GST 28%',
      rate: 28,
      type: 'percentage' as const,
      description: 'Goods and Services Tax - 28%',
      category: 'GST',
    },
    {
      name: 'Service Charge',
      rate: 10,
      type: 'percentage' as const,
      description: 'Service charge for dine-in',
      category: 'Service',
    },
    {
      name: 'Delivery Fee',
      rate: 50,
      type: 'fixed' as const,
      description: 'Fixed delivery charge',
      category: 'Service',
    },
    {
      name: 'VAT 12.5%',
      rate: 12.5,
      type: 'percentage' as const,
      description: 'Value Added Tax - 12.5%',
      category: 'VAT',
    },
    {
      name: 'VAT 14.5%',
      rate: 14.5,
      type: 'percentage' as const,
      description: 'Value Added Tax - 14.5%',
      category: 'VAT',
    },
    {
      name: 'Local Tax',
      rate: 2,
      type: 'percentage' as const,
      description: 'Local municipal tax',
      category: 'Local',
    },
  ];

  useEffect(() => {
    loadTaxRates();
  }, [restaurantId]);

  useEffect(() => {
    if (toast) {
      const timer = setTimeout(() => {
        setToast(null);
      }, toast.duration || 5000);
      return () => clearTimeout(timer);
    }
  }, [toast]);

  const showToast = useCallback(
    (type: 'success' | 'error' | 'info' | 'warning', message: string, duration?: number) => {
      setToast({ type, message, duration });
    },
    []
  );

  // Filter tax rates based on search and filters
  const filteredTaxRates = taxRates.filter(tax => {
    const matchesSearch = tax.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || tax.type === filterType;
    const matchesStatus =
      filterStatus === 'all' ||
      (filterStatus === 'active' && tax.isActive) ||
      (filterStatus === 'inactive' && !tax.isActive);

    return matchesSearch && matchesType && matchesStatus;
  });

  const loadTaxRates = async () => {
    try {
      setIsLoading(true);
      const data = await window.electronAPI.getTaxRates(restaurantId);

      if (!Array.isArray(data)) {
        throw new Error('Invalid data format received from server');
      }

      setTaxRates(data);

      if (data.length === 0) {
        showToast('info', 'No tax rates found. Add your first tax rate to get started.', 3000);
      }
    } catch (error) {
      console.error('Failed to load tax rates:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      showToast(
        'error',
        `Failed to load tax rates: ${errorMessage}. Please check your connection and try again.`
      );
      setTaxRates([]); // Ensure we have a valid state
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      rate: '',
      type: 'percentage',
      isDefault: false,
      isActive: true,
    });
    setEditingTax(null);
    setShowAddForm(false);
    setFormErrors({});
  };

  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    // Validate tax name
    const trimmedName = formData.name.trim();
    if (!trimmedName) {
      errors.name = 'Tax name is required';
    } else if (trimmedName.length < 2) {
      errors.name = 'Tax name must be at least 2 characters';
    } else if (trimmedName.length > 50) {
      errors.name = 'Tax name must be less than 50 characters';
    } else if (!/^[a-zA-Z0-9\s%.-]+$/.test(trimmedName)) {
      errors.name = 'Tax name contains invalid characters';
    }

    // Validate tax rate
    if (!formData.rate.trim()) {
      errors.rate = 'Tax rate is required';
    } else {
      const rate = parseFloat(formData.rate);
      if (isNaN(rate)) {
        errors.rate = 'Please enter a valid number';
      } else if (rate < 0) {
        errors.rate = 'Tax rate cannot be negative';
      } else if (rate === 0) {
        errors.rate = 'Tax rate must be greater than 0';
      } else if (formData.type === 'percentage') {
        if (rate > 100) {
          errors.rate = 'Percentage rate cannot exceed 100%';
        } else if (rate > 50) {
          // Warning for high percentage rates
          showToast(
            'warning',
            'High percentage rate detected. Please verify this is correct.',
            3000
          );
        }
      } else if (formData.type === 'fixed') {
        if (rate > 10000) {
          errors.rate = 'Fixed amount seems too high. Please verify.';
        } else if (rate < 1) {
          errors.rate = 'Fixed amount must be at least ₹1';
        }
      }
    }

    // Check for duplicate tax names (excluding current editing tax)
    const duplicateTax = taxRates.find(
      tax => tax.name.toLowerCase() === trimmedName.toLowerCase() && tax.id !== editingTax?.id
    );
    if (duplicateTax) {
      errors.name = `A tax with the name "${duplicateTax.name}" already exists`;
    }

    // Validate default tax logic
    if (formData.isDefault && !formData.isActive) {
      errors.isActive = 'Default tax rate must be active';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      showToast('error', 'Please fix the errors below before submitting.');
      return;
    }

    setIsSaving(true);

    try {
      const rate = parseFloat(formData.rate);
      const taxData = {
        name: formData.name.trim(),
        rate: rate,
        type: formData.type,
        isDefault: formData.isDefault,
        isActive: formData.isActive,
        restaurantId,
      };

      let result;
      let actionType = '';

      if (editingTax) {
        result = await window.electronAPI.updateTaxRate(editingTax.id, taxData);
        actionType = 'update';
      } else {
        result = await window.electronAPI.createTaxRate(taxData);
        actionType = 'create';
      }

      if (!result) {
        throw new Error('No response received from server');
      }

      if (result.success) {
        await loadTaxRates();
        resetForm();

        const successMessage =
          actionType === 'update'
            ? `Tax rate "${taxData.name}" updated successfully!`
            : `Tax rate "${taxData.name}" created successfully!`;

        showToast('success', successMessage);

        // Additional success feedback for default tax
        if (taxData.isDefault) {
          showToast('info', `"${taxData.name}" is now your default tax rate.`, 3000);
        }
      } else {
        const errorMessage = result.error || 'Unknown error occurred';
        showToast('error', `Failed to ${actionType} tax rate: ${errorMessage}`);
      }
    } catch (error) {
      console.error('Error saving tax rate:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      showToast(
        'error',
        `Failed to save tax rate: ${errorMessage}. Please check your connection and try again.`
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleEdit = (tax: TaxRate) => {
    setFormData({
      name: tax.name,
      rate: tax.rate.toString(),
      type: tax.type,
      isDefault: tax.isDefault,
      isActive: tax.isActive,
    });
    setEditingTax(tax);
    setShowAddForm(true);
  };

  const handleDelete = async (id: string, taxName: string) => {
    // Enhanced confirmation dialog
    const confirmMessage = `Are you sure you want to delete the tax rate "${taxName}"?\n\nThis action cannot be undone and may affect existing orders that use this tax rate.`;

    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      setIsSaving(true);
      const result = await window.electronAPI.deleteTaxRate(id);

      if (!result) {
        throw new Error('No response received from server');
      }

      if (result.success) {
        await loadTaxRates();
        showToast('success', `Tax rate "${taxName}" deleted successfully!`);
      } else {
        const errorMessage = result.error || 'Unknown error occurred';
        showToast('error', `Failed to delete tax rate: ${errorMessage}`);
      }
    } catch (error) {
      console.error('Error deleting tax rate:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      showToast('error', `Failed to delete tax rate: ${errorMessage}. Please try again.`);
    } finally {
      setIsSaving(false);
    }
  };

  const toggleTaxStatus = async (tax: TaxRate) => {
    try {
      setIsSaving(true);

      // Check if deactivating the default tax
      if (tax.isDefault && tax.isActive) {
        const confirmMessage = `"${tax.name}" is currently the default tax rate. Deactivating it will remove its default status.\n\nDo you want to continue?`;
        if (!confirm(confirmMessage)) {
          return;
        }
      }

      const result = await window.electronAPI.updateTaxRate(tax.id, {
        isActive: !tax.isActive,
        isDefault: tax.isDefault && !tax.isActive ? false : tax.isDefault, // Remove default if deactivating
      });

      if (!result) {
        throw new Error('No response received from server');
      }

      if (result.success) {
        await loadTaxRates();
        const status = !tax.isActive ? 'activated' : 'deactivated';
        showToast('success', `Tax rate "${tax.name}" ${status} successfully!`);

        if (tax.isDefault && !tax.isActive) {
          showToast(
            'warning',
            'Default tax status removed. Please set a new default tax rate.',
            5000
          );
        }
      } else {
        const errorMessage = result.error || 'Unknown error occurred';
        showToast('error', `Failed to update tax status: ${errorMessage}`);
      }
    } catch (error) {
      console.error('Error updating tax status:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      showToast('error', `Failed to update tax status: ${errorMessage}. Please try again.`);
    } finally {
      setIsSaving(false);
    }
  };

  const setAsDefault = async (tax: TaxRate) => {
    try {
      setIsSaving(true);

      // Confirm if there's already a default tax
      const currentDefault = taxRates.find(t => t.isDefault);
      if (currentDefault && currentDefault.id !== tax.id) {
        const confirmMessage = `"${currentDefault.name}" is currently the default tax rate.\n\nDo you want to replace it with "${tax.name}"?`;
        if (!confirm(confirmMessage)) {
          return;
        }
      }

      // The backend handles removing default from other taxes automatically
      const result = await window.electronAPI.updateTaxRate(tax.id, { isDefault: true });

      if (!result) {
        throw new Error('No response received from server');
      }

      if (result.success) {
        await loadTaxRates();
        showToast('success', `"${tax.name}" set as default tax rate!`);

        if (currentDefault && currentDefault.id !== tax.id) {
          showToast('info', `"${currentDefault.name}" is no longer the default tax rate.`, 3000);
        }
      } else {
        const errorMessage = result.error || 'Unknown error occurred';
        showToast('error', `Failed to set default tax: ${errorMessage}`);
      }
    } catch (error) {
      console.error('Error setting default tax:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      showToast('error', `Failed to set default tax: ${errorMessage}. Please try again.`);
    } finally {
      setIsSaving(false);
    }
  };

  const addCommonTax = async (commonTax: (typeof commonTaxRates)[0]) => {
    // Check if this tax already exists
    const existingTax = taxRates.find(
      tax => tax.name.toLowerCase() === commonTax.name.toLowerCase()
    );

    if (existingTax) {
      showToast('info', `Tax "${commonTax.name}" already exists`);
      return;
    }

    // Directly save common tax without opening form
    setIsSaving(true);
    try {
      const taxData = {
        restaurantId,
        name: commonTax.name,
        rate: commonTax.rate,
        type: commonTax.type,
        isDefault: taxRates.length === 0, // Make first tax default
        isActive: true,
      };

      const result = await window.electronAPI.createTaxRate(taxData);
      if (result.success) {
        await loadTaxRates();
        showToast('success', `Tax "${commonTax.name}" added successfully!`);
      } else {
        showToast('error', `Failed to add tax: ${result.error}`);
      }
    } catch (error) {
      console.error('Error adding common tax:', error);
      showToast('error', 'Failed to add tax. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="settings-loading">
        <div className="loading-spinner"></div>
        <p>Loading tax rates...</p>
      </div>
    );
  }

  return (
    <div className="tax-management">
      {/* Toast Notification */}
      {toast && (
        <div className={`toast toast-${toast.type}`}>
          <div className="toast-content">
            <span className="toast-icon">
              {toast.type === 'success' && <Icon name="check-circle" size="sm" />}
              {toast.type === 'error' && <Icon name="x-circle" size="sm" />}
              {toast.type === 'info' && <Icon name="info" size="sm" />}
              {toast.type === 'warning' && <Icon name="warning" size="sm" />}
            </span>
            <span className="toast-message">{toast.message}</span>
            <button className="toast-close" onClick={() => setToast(null)}>
              ×
            </button>
          </div>
        </div>
      )}

      {/* Header Section */}
      <div className="tax-management-header">
        <div className="header-content">
          <div className="header-info">
            <h3 className="section-title">
              <Icon name="receipt" size="md" />
              Tax Configuration
            </h3>
            <p className="section-description">
              Manage tax rates for your restaurant. Configure GST, VAT, service charges, and other
              applicable taxes.
            </p>
            <div className="tax-stats">
              <div className="stat-item">
                <span className="stat-number">{taxRates.length}</span>
                <span className="stat-label">Total Taxes</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">{taxRates.filter(t => t.isActive).length}</span>
                <span className="stat-label">Active</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">{taxRates.filter(t => t.isDefault).length}</span>
                <span className="stat-label">Default</span>
              </div>
            </div>
          </div>
          <div className="header-actions">
            <button
              className="btn btn-secondary"
              onClick={() => loadTaxRates()}
              disabled={isLoading}
              title="Refresh tax rates"
            >
              <Icon name="loading" size="sm" />
              Refresh
            </button>
            <button
              className="btn btn-primary"
              onClick={() => setShowAddForm(true)}
              disabled={isSaving}
            >
              <Icon name="plus" size="sm" />
              Add Tax Rate
            </button>
          </div>
        </div>
      </div>

      {/* Quick Add Common Taxes */}
      <div className="common-taxes">
        <div className="common-taxes-header">
          <h4>
            <Icon name="star" size="sm" />
            Quick Add Common Taxes
          </h4>
          <p className="common-taxes-description">
            Click on any preset to quickly add common tax rates to your system.
          </p>
        </div>

        <div className="common-tax-categories">
          {['GST', 'VAT', 'Service', 'Local'].map(category => (
            <div key={category} className="tax-category">
              <h5 className="category-title">{category}</h5>
              <div className="category-buttons">
                {commonTaxRates
                  .filter(tax => tax.category === category)
                  .map((tax, index) => (
                    <button
                      key={index}
                      className="common-tax-btn"
                      onClick={() => addCommonTax(tax)}
                      title={tax.description}
                      disabled={
                        isSaving ||
                        taxRates.some(t => t.name.toLowerCase() === tax.name.toLowerCase())
                      }
                    >
                      <div className="tax-btn-content">
                        <span className="tax-btn-name">{tax.name}</span>
                        <span className="tax-btn-rate">
                          {tax.type === 'percentage' ? `${tax.rate}%` : `₹${tax.rate}`}
                        </span>
                      </div>
                      {taxRates.some(t => t.name.toLowerCase() === tax.name.toLowerCase()) && (
                        <Icon name="check-circle" size="xs" className="added-icon" />
                      )}
                    </button>
                  ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="tax-controls">
        <div className="search-section">
          <div className="search-input-wrapper">
            <Icon name="search" size="sm" />
            <input
              type="text"
              placeholder="Search tax rates..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="search-input"
            />
            {searchTerm && (
              <button
                className="clear-search"
                onClick={() => setSearchTerm('')}
                title="Clear search"
              >
                <Icon name="cancel" size="xs" />
              </button>
            )}
          </div>
        </div>

        <div className="filter-section">
          <select
            value={filterType}
            onChange={e => setFilterType(e.target.value as any)}
            className="filter-select"
          >
            <option value="all">All Types</option>
            <option value="percentage">Percentage</option>
            <option value="fixed">Fixed Amount</option>
          </select>

          <select
            value={filterStatus}
            onChange={e => setFilterStatus(e.target.value as any)}
            className="filter-select"
          >
            <option value="all">All Status</option>
            <option value="active">Active Only</option>
            <option value="inactive">Inactive Only</option>
          </select>
        </div>
      </div>

      {showAddForm && (
        <div className="tax-form-modal">
          <div className="tax-form-overlay" onClick={resetForm}></div>
          <div className="tax-form-container">
            <div className="tax-form-header">
              <h3>{editingTax ? 'Edit Tax Rate' : 'Add New Tax Rate'}</h3>
              <button className="close-btn" onClick={resetForm}>
                ×
              </button>
            </div>

            <form onSubmit={handleSubmit} className="tax-form">
              <div className="form-group">
                <label className="form-label">Tax Name *</label>
                <input
                  type="text"
                  className={`form-input ${formErrors.name ? 'error' : ''}`}
                  value={formData.name}
                  onChange={e => {
                    setFormData({ ...formData, name: e.target.value });
                    if (formErrors.name) {
                      setFormErrors({ ...formErrors, name: '' });
                    }
                  }}
                  placeholder="e.g., GST 18%, Service Charge"
                  required
                />
                {formErrors.name && <span className="error-message">{formErrors.name}</span>}
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label className="form-label">Tax Type</label>
                  <select
                    className="form-select"
                    value={formData.type}
                    onChange={e =>
                      setFormData({ ...formData, type: e.target.value as 'percentage' | 'fixed' })
                    }
                  >
                    <option value="percentage">Percentage (%)</option>
                    <option value="fixed">Fixed Amount (₹)</option>
                  </select>
                </div>
                <div className="form-group">
                  <label className="form-label">
                    Rate * {formData.type === 'percentage' ? '(%)' : '(₹)'}
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    max={formData.type === 'percentage' ? '100' : undefined}
                    className={`form-input ${formErrors.rate ? 'error' : ''}`}
                    value={formData.rate}
                    onChange={e => {
                      setFormData({ ...formData, rate: e.target.value });
                      if (formErrors.rate) {
                        setFormErrors({ ...formErrors, rate: '' });
                      }
                    }}
                    placeholder={formData.type === 'percentage' ? '18' : '50'}
                    required
                  />
                  {formErrors.rate && <span className="error-message">{formErrors.rate}</span>}
                </div>
              </div>

              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={formData.isDefault}
                    onChange={e => {
                      console.log('Default checkbox changed:', e.target.checked);
                      setFormData({ ...formData, isDefault: e.target.checked });
                    }}
                  />
                  <div className="checkbox-content">
                    <span className="checkbox-text">Set as default tax rate</span>
                    <span className="checkbox-description">
                      This will be automatically applied to new items
                    </span>
                  </div>
                </label>
              </div>

              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={e => {
                      console.log('Active checkbox changed:', e.target.checked);
                      setFormData({ ...formData, isActive: e.target.checked });
                    }}
                  />
                  <div className="checkbox-content">
                    <span className="checkbox-text">Active</span>
                    <span className="checkbox-description">
                      Only active tax rates can be applied
                    </span>
                  </div>
                </label>
              </div>

              <div className="form-actions">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={resetForm}
                  disabled={isSaving}
                >
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary" disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <span className="loading-spinner"></span>
                      {editingTax ? 'Updating...' : 'Creating...'}
                    </>
                  ) : (
                    <>{editingTax ? 'Update' : 'Create'} Tax Rate</>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Tax Rates List */}
      <div className="tax-rates-section">
        <div className="section-header">
          <h4>
            <Icon name="list" size="sm" />
            Tax Rates ({filteredTaxRates.length})
          </h4>
          {searchTerm && (
            <p className="search-results">
              Showing {filteredTaxRates.length} of {taxRates.length} tax rates
            </p>
          )}
        </div>

        {taxRates.length === 0 ? (
          <div className="empty-taxes">
            <div className="empty-content">
              <Icon name="receipt" size="xl" className="empty-icon" />
              <h3>No tax rates configured</h3>
              <p>Start by adding tax rates to calculate taxes on orders automatically.</p>
              <div className="empty-actions">
                <button className="btn btn-primary" onClick={() => setShowAddForm(true)}>
                  <Icon name="plus" size="sm" />
                  Add Your First Tax Rate
                </button>
              </div>
            </div>
          </div>
        ) : filteredTaxRates.length === 0 ? (
          <div className="empty-taxes">
            <div className="empty-content">
              <Icon name="search" size="xl" className="empty-icon" />
              <h3>No matching tax rates found</h3>
              <p>Try adjusting your search or filter criteria.</p>
              <div className="empty-actions">
                <button
                  className="btn btn-secondary"
                  onClick={() => {
                    setSearchTerm('');
                    setFilterType('all');
                    setFilterStatus('all');
                  }}
                >
                  <Icon name="cancel" size="sm" />
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="tax-cards">
            {filteredTaxRates.map(tax => (
              <div
                key={tax.id}
                className={`tax-card ${!tax.isActive ? 'inactive' : ''} ${tax.isDefault ? 'default' : ''}`}
              >
                <div className="tax-card-header">
                  <div className="tax-info">
                    <div className="tax-name">{tax.name}</div>
                    <div className="tax-meta">
                      <span className="tax-id">ID: {tax.id.slice(-8)}</span>
                      <span className="tax-created">
                        Created: {new Date(tax.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  <div className="tax-badges">
                    {tax.isDefault && (
                      <span className="badge default-badge">
                        <Icon name="star" size="xs" />
                        Default
                      </span>
                    )}
                    {!tax.isActive && (
                      <span className="badge inactive-badge">
                        <Icon name="x-circle" size="xs" />
                        Inactive
                      </span>
                    )}
                    <span className={`badge type-badge ${tax.type}`}>
                      <Icon name={tax.type === 'percentage' ? 'target' : 'payment'} size="xs" />
                      {tax.type === 'percentage' ? 'Percentage' : 'Fixed'}
                    </span>
                  </div>
                </div>

                <div className="tax-card-body">
                  <div className="tax-rate-display">
                    <div className="tax-rate">
                      {tax.type === 'percentage' ? `${tax.rate}%` : `₹${tax.rate}`}
                    </div>
                    <div className="tax-type">
                      {tax.type === 'percentage' ? 'Percentage Rate' : 'Fixed Amount'}
                    </div>
                  </div>
                </div>

                <div className="tax-card-actions">
                  <button
                    className={`action-btn status-btn ${tax.isActive ? 'active' : 'inactive'}`}
                    onClick={() => toggleTaxStatus(tax)}
                    disabled={isSaving}
                    title={tax.isActive ? 'Deactivate tax rate' : 'Activate tax rate'}
                  >
                    <Icon name={tax.isActive ? 'check-circle' : 'x-circle'} size="sm" />
                    {tax.isActive ? 'Active' : 'Inactive'}
                  </button>

                  {!tax.isDefault && tax.isActive && (
                    <button
                      className="action-btn default-btn"
                      onClick={() => setAsDefault(tax)}
                      disabled={isSaving}
                      title="Set as default tax rate"
                    >
                      <Icon name="star" size="sm" />
                      Set Default
                    </button>
                  )}

                  <button
                    className="action-btn edit-btn"
                    onClick={() => handleEdit(tax)}
                    disabled={isSaving}
                    title="Edit tax rate"
                  >
                    <Icon name="edit" size="sm" />
                    Edit
                  </button>

                  <button
                    className="action-btn delete-btn"
                    onClick={() => handleDelete(tax.id, tax.name)}
                    disabled={isSaving || tax.isDefault}
                    title={tax.isDefault ? 'Cannot delete default tax rate' : 'Delete tax rate'}
                  >
                    <Icon name="trash" size="sm" />
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TaxManagement;
