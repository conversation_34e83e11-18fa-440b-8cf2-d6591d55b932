import React, { useState, useEffect } from 'react';
import { UserDetails, RestaurantDetails } from '../../types';
import ImageUpload from '../ImageUpload';

interface UserProfileProps {
  userDetails: UserDetails;
  restaurantDetails: RestaurantDetails;
  onUpdateUser?: (updates: Partial<UserDetails>) => void;
  onUpdateRestaurant?: (updates: Partial<RestaurantDetails>) => void;
}

interface FormErrors {
  [key: string]: string;
}

interface ToastMessage {
  type: 'success' | 'error' | 'info';
  message: string;
}

const UserProfile: React.FC<UserProfileProps> = ({
  userDetails,
  restaurantDetails,
  onUpdateUser,
  onUpdateRestaurant,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [toast, setToast] = useState<ToastMessage | null>(null);

  const [userForm, setUserForm] = useState({
    name: userDetails.name || userDetails.fullName || '',
    email: userDetails.email || '',
    phone: userDetails.phone || '',
    address: userDetails.address || '',
    profileImage: userDetails.profileImage || '',
  });

  const [restaurantForm, setRestaurantForm] = useState({
    restaurantName: restaurantDetails.restaurantName || '',
    restaurantType: restaurantDetails.restaurantType || '',
    location: restaurantDetails.location || '',
    restaurantAddress: restaurantDetails.restaurantAddress || '',
    phone: restaurantDetails.phone || '',
    email: restaurantDetails.email || '',
    website: restaurantDetails.website || '',
    description: restaurantDetails.description || '',
  });

  // Update form data when props change
  useEffect(() => {
    setUserForm({
      name: userDetails.name || userDetails.fullName || '',
      email: userDetails.email || '',
      phone: userDetails.phone || '',
      address: userDetails.address || '',
      profileImage: userDetails.profileImage || '',
    });
    setRestaurantForm({
      restaurantName: restaurantDetails.restaurantName || '',
      restaurantType: restaurantDetails.restaurantType || '',
      location: restaurantDetails.location || '',
      restaurantAddress: restaurantDetails.restaurantAddress || '',
      phone: restaurantDetails.phone || '',
      email: restaurantDetails.email || '',
      website: restaurantDetails.website || '',
      description: restaurantDetails.description || '',
    });
  }, [userDetails, restaurantDetails]);

  // Auto-hide toast after 5 seconds
  useEffect(() => {
    if (toast) {
      const timer = setTimeout(() => setToast(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [toast]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // User validation
    if (!userForm.name.trim()) {
      newErrors.name = 'Name is required';
    }
    if (!userForm.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userForm.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    if (!userForm.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    // Restaurant validation
    if (!restaurantForm.restaurantName.trim()) {
      newErrors.restaurantName = 'Restaurant name is required';
    }
    if (!restaurantForm.restaurantType) {
      newErrors.restaurantType = 'Restaurant type is required';
    }
    if (!restaurantForm.location.trim()) {
      newErrors.location = 'Location is required';
    }

    // Website URL validation
    if (restaurantForm.website && !/^https?:\/\/.+/.test(restaurantForm.website)) {
      newErrors.website = 'Please enter a valid website URL (starting with http:// or https://)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      setToast({ type: 'error', message: 'Please fix the errors before saving' });
      return;
    }

    setIsSaving(true);
    setErrors({});

    try {
      const promises = [];

      if (onUpdateUser) {
        promises.push(onUpdateUser(userForm));
      }
      if (onUpdateRestaurant) {
        promises.push(onUpdateRestaurant(restaurantForm));
      }

      await Promise.all(promises);

      setIsEditing(false);
      setToast({ type: 'success', message: 'Profile updated successfully!' });
    } catch (error) {
      console.error('Error updating profile:', error);
      setToast({ type: 'error', message: 'Failed to update profile. Please try again.' });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setUserForm({
      name: userDetails.name || userDetails.fullName || '',
      email: userDetails.email || '',
      phone: userDetails.phone || '',
      address: userDetails.address || '',
      profileImage: userDetails.profileImage || '',
    });
    setRestaurantForm({
      restaurantName: restaurantDetails.restaurantName || '',
      restaurantType: restaurantDetails.restaurantType || '',
      location: restaurantDetails.location || '',
      restaurantAddress: restaurantDetails.restaurantAddress || '',
      phone: restaurantDetails.phone || '',
      email: restaurantDetails.email || '',
      website: restaurantDetails.website || '',
      description: restaurantDetails.description || '',
    });
    setErrors({});
    setIsEditing(false);
  };

  const getSubscriptionStatusBadge = () => {
    const status = userDetails.subscriptionStatus || 'trial';
    const statusConfig = {
      trial: { label: 'Trial', className: 'status-badge trial', icon: '🆓' },
      active: { label: 'Active', className: 'status-badge active', icon: '✅' },
      expired: { label: 'Expired', className: 'status-badge expired', icon: '⚠️' },
      cancelled: { label: 'Cancelled', className: 'status-badge cancelled', icon: '❌' },
    };

    const config = statusConfig[status];
    return (
      <span className={config.className}>
        {config.icon} {config.label}
      </span>
    );
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="modern-user-profile">
      {/* Toast Notification */}
      {toast && (
        <div className={`modern-toast modern-toast-${toast.type}`}>
          <div className="toast-content">
            <span className="toast-icon">
              {toast.type === 'success' && '✅'}
              {toast.type === 'error' && '❌'}
              {toast.type === 'info' && 'ℹ️'}
            </span>
            <span className="toast-message">{toast.message}</span>
            <button className="toast-close" onClick={() => setToast(null)}>
              ×
            </button>
          </div>
        </div>
      )}

      {/* Modern Profile Header Card */}
      <div className="modern-profile-card">
        <div className="profile-card-header">
          <div className="profile-avatar-section">
            <div className="modern-profile-avatar">
              {userForm.profileImage ? (
                <img src={userForm.profileImage} alt="Profile" className="avatar-image" />
              ) : (
                <div className="avatar-placeholder">
                  <span className="avatar-icon">👤</span>
                </div>
              )}
              {isEditing && (
                <div className="avatar-edit-overlay">
                  <span className="edit-icon">📷</span>
                </div>
              )}
            </div>
            <div className="profile-info">
              <h1 className="profile-name">{userDetails.name || userDetails.fullName || 'User'}</h1>
              <p className="profile-role">Restaurant Owner</p>
              <div className="profile-badges">{getSubscriptionStatusBadge()}</div>
            </div>
          </div>
          <div className="profile-actions">
            {!isEditing ? (
              <button
                className="modern-btn modern-btn-primary"
                onClick={() => setIsEditing(true)}
                disabled={isSaving}
              >
                <span className="btn-icon">✏️</span>
                Edit Profile
              </button>
            ) : (
              <div className="edit-actions">
                <button
                  className="modern-btn modern-btn-secondary"
                  onClick={handleCancel}
                  disabled={isSaving}
                >
                  Cancel
                </button>
                <button
                  className="modern-btn modern-btn-primary"
                  onClick={handleSave}
                  disabled={isSaving}
                >
                  <span className="btn-icon">{isSaving ? '⏳' : '💾'}</span>
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="profile-stats">
          <div className="stat-item">
            <div className="stat-icon">📅</div>
            <div className="stat-content">
              <span className="stat-label">Member Since</span>
              <span className="stat-value">
                {userDetails.trialStartDate ? formatDate(userDetails.trialStartDate) : 'N/A'}
              </span>
            </div>
          </div>
          <div className="stat-item">
            <div className="stat-icon">🏪</div>
            <div className="stat-content">
              <span className="stat-label">Restaurant</span>
              <span className="stat-value">{restaurantDetails.restaurantName || 'Not set'}</span>
            </div>
          </div>

          {userDetails.subscriptionStatus === 'trial' && userDetails.trialEndDate && (
            <div className="stat-item trial-warning">
              <div className="stat-icon">⏰</div>
              <div className="stat-content">
                <span className="stat-label">Trial Ends</span>
                <span className="stat-value">{formatDate(userDetails.trialEndDate)}</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modern Profile Sections */}
      <div className="modern-profile-sections">
        {/* Personal Information Card */}
        <div className="modern-section-card">
          <div className="section-header">
            <div className="section-icon">👤</div>
            <h3 className="section-title">Personal Information</h3>
          </div>

          {isEditing && (
            <div className="image-upload-section">
              <ImageUpload
                currentImage={userForm.profileImage}
                onImageChange={imageUrl => setUserForm({ ...userForm, profileImage: imageUrl })}
                onImageRemove={() => setUserForm({ ...userForm, profileImage: '' })}
                label="Profile Image"
                placeholder="No profile image selected"
              />
            </div>
          )}

          <div className="modern-form-grid">
            <div className="modern-form-group">
              <label className="modern-form-label">
                <span className="label-icon">👤</span>
                Full Name *
              </label>
              {isEditing ? (
                <div>
                  <input
                    type="text"
                    className={`modern-form-input ${errors.name ? 'error' : ''}`}
                    value={userForm.name}
                    onChange={e => setUserForm({ ...userForm, name: e.target.value })}
                    placeholder="Enter your full name"
                  />
                  {errors.name && <span className="modern-error-message">{errors.name}</span>}
                </div>
              ) : (
                <div className="modern-form-display">
                  {userDetails.name || userDetails.fullName || 'Not set'}
                </div>
              )}
            </div>

            <div className="modern-form-group">
              <label className="modern-form-label">
                <span className="label-icon">📧</span>
                Email Address *
              </label>
              {isEditing ? (
                <div>
                  <input
                    type="email"
                    className={`modern-form-input ${errors.email ? 'error' : ''}`}
                    value={userForm.email}
                    onChange={e => setUserForm({ ...userForm, email: e.target.value })}
                    placeholder="Enter your email"
                  />
                  {errors.email && <span className="modern-error-message">{errors.email}</span>}
                </div>
              ) : (
                <div className="modern-form-display">{userDetails.email || 'Not set'}</div>
              )}
            </div>

            <div className="modern-form-group">
              <label className="modern-form-label">
                <span className="label-icon">📱</span>
                Phone Number
              </label>
              {isEditing ? (
                <div>
                  <input
                    type="tel"
                    className={`modern-form-input ${errors.phone ? 'error' : ''}`}
                    value={userForm.phone}
                    onChange={e => setUserForm({ ...userForm, phone: e.target.value })}
                    placeholder="Enter your phone number"
                  />
                  {errors.phone && <span className="modern-error-message">{errors.phone}</span>}
                </div>
              ) : (
                <div className="modern-form-display">{userDetails.phone || 'Not set'}</div>
              )}
            </div>

            <div className="modern-form-group full-width">
              <label className="modern-form-label">
                <span className="label-icon">📍</span>
                Address
              </label>
              {isEditing ? (
                <textarea
                  className="modern-form-input"
                  rows={2}
                  value={userForm.address}
                  onChange={e => setUserForm({ ...userForm, address: e.target.value })}
                  placeholder="Enter your address"
                />
              ) : (
                <div className="modern-form-display">{userDetails.address || 'Not set'}</div>
              )}
            </div>

            <div className="modern-form-group">
              <label className="modern-form-label">
                <span className="label-icon">📅</span>
                Account Created
              </label>
              <div className="modern-form-display readonly">
                {userDetails.trialStartDate
                  ? formatDate(userDetails.trialStartDate)
                  : 'Not available'}
              </div>
            </div>
          </div>
        </div>

        {/* Restaurant Information Card */}
        <div className="modern-section-card">
          <div className="section-header">
            <div className="section-icon">🏪</div>
            <h3 className="section-title">Restaurant Information</h3>
          </div>

          <div className="modern-form-grid">
            <div className="modern-form-group">
              <label className="modern-form-label">
                <span className="label-icon">🏪</span>
                Restaurant Name *
              </label>
              {isEditing ? (
                <div>
                  <input
                    type="text"
                    className={`modern-form-input ${errors.restaurantName ? 'error' : ''}`}
                    value={restaurantForm.restaurantName}
                    onChange={e =>
                      setRestaurantForm({ ...restaurantForm, restaurantName: e.target.value })
                    }
                    placeholder="Enter restaurant name"
                  />
                  {errors.restaurantName && (
                    <span className="modern-error-message">{errors.restaurantName}</span>
                  )}
                </div>
              ) : (
                <div className="modern-form-display">
                  {restaurantDetails.restaurantName || 'Not set'}
                </div>
              )}
            </div>

            <div className="form-group">
              <label className="form-label">Restaurant Type *</label>
              {isEditing ? (
                <div>
                  <select
                    className={`form-select ${errors.restaurantType ? 'error' : ''}`}
                    value={restaurantForm.restaurantType}
                    onChange={e =>
                      setRestaurantForm({
                        ...restaurantForm,
                        restaurantType: e.target.value as RestaurantDetails['restaurantType'],
                      })
                    }
                  >
                    <option value="">Select type</option>
                    <option value="Fine Dining">Fine Dining</option>
                    <option value="Casual Dining">Casual Dining</option>
                    <option value="Fast Food">Fast Food</option>
                    <option value="Café">Café</option>
                    <option value="Takeaway">Takeaway</option>
                    <option value="Dine-In">Dine-In</option>
                    <option value="Bar & Grill">Bar & Grill</option>
                    <option value="Bakery">Bakery</option>
                    <option value="Food Truck">Food Truck</option>
                    <option value="Other">Other</option>
                  </select>
                  {errors.restaurantType && (
                    <span className="error-message">{errors.restaurantType}</span>
                  )}
                </div>
              ) : (
                <div className="form-display">{restaurantDetails.restaurantType || 'Not set'}</div>
              )}
            </div>

            <div className="form-group">
              <label className="form-label">Location *</label>
              {isEditing ? (
                <div>
                  <input
                    type="text"
                    className={`form-input ${errors.location ? 'error' : ''}`}
                    value={restaurantForm.location}
                    onChange={e =>
                      setRestaurantForm({ ...restaurantForm, location: e.target.value })
                    }
                    placeholder="Enter location (city, area)"
                  />
                  {errors.location && <span className="error-message">{errors.location}</span>}
                </div>
              ) : (
                <div className="form-display">{restaurantDetails.location || 'Not set'}</div>
              )}
            </div>

            <div className="form-group">
              <label className="form-label">Full Address</label>
              {isEditing ? (
                <textarea
                  className="form-input"
                  rows={2}
                  value={restaurantForm.restaurantAddress}
                  onChange={e =>
                    setRestaurantForm({ ...restaurantForm, restaurantAddress: e.target.value })
                  }
                  placeholder="Enter complete restaurant address"
                />
              ) : (
                <div className="form-display">
                  {restaurantDetails.restaurantAddress || 'Not set'}
                </div>
              )}
            </div>

            <div className="form-group">
              <label className="form-label">Restaurant Phone</label>
              {isEditing ? (
                <input
                  type="tel"
                  className="form-input"
                  value={restaurantForm.phone}
                  onChange={e => setRestaurantForm({ ...restaurantForm, phone: e.target.value })}
                  placeholder="Enter restaurant phone"
                />
              ) : (
                <div className="form-display">{restaurantDetails.phone || 'Not set'}</div>
              )}
            </div>

            <div className="form-group">
              <label className="form-label">Restaurant Email</label>
              {isEditing ? (
                <input
                  type="email"
                  className="form-input"
                  value={restaurantForm.email}
                  onChange={e => setRestaurantForm({ ...restaurantForm, email: e.target.value })}
                  placeholder="Enter restaurant email"
                />
              ) : (
                <div className="form-display">{restaurantDetails.email || 'Not set'}</div>
              )}
            </div>

            <div className="form-group">
              <label className="form-label">Website</label>
              {isEditing ? (
                <div>
                  <input
                    type="url"
                    className={`form-input ${errors.website ? 'error' : ''}`}
                    value={restaurantForm.website}
                    onChange={e =>
                      setRestaurantForm({ ...restaurantForm, website: e.target.value })
                    }
                    placeholder="https://example.com"
                  />
                  {errors.website && <span className="error-message">{errors.website}</span>}
                </div>
              ) : (
                <div className="form-display">
                  {restaurantDetails.website ? (
                    <a
                      href={restaurantDetails.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="website-link"
                    >
                      {restaurantDetails.website}
                    </a>
                  ) : (
                    'Not set'
                  )}
                </div>
              )}
            </div>

            <div className="form-group full-width">
              <label className="form-label">Description</label>
              {isEditing ? (
                <textarea
                  className="form-input"
                  rows={3}
                  value={restaurantForm.description}
                  onChange={e =>
                    setRestaurantForm({ ...restaurantForm, description: e.target.value })
                  }
                  placeholder="Describe your restaurant, cuisine type, specialties, etc."
                />
              ) : (
                <div className="form-display">{restaurantDetails.description || 'Not set'}</div>
              )}
            </div>
          </div>
        </div>

        <div className="settings-section">
          <h3 className="section-title">Account Security</h3>
          <div className="security-info">
            <div className="security-item">
              <span className="security-icon">🔐</span>
              <div className="security-content">
                <h4>PIN Protection</h4>
                <p>Your account is protected with a secure PIN</p>
                <small className="help-text">PIN is required to access the application</small>
              </div>
              <button className="btn btn-secondary" disabled>
                Change PIN
              </button>
            </div>

            <div className="security-item">
              <span className="security-icon">🔑</span>
              <div className="security-content">
                <h4>Device Authorization</h4>
                <p>This device is authorized for your restaurant</p>
                <small className="help-text">Device is registered and active</small>
              </div>
              <span className="status-badge active">✅ Active</span>
            </div>

            <div className="security-item">
              <span className="security-icon">📊</span>
              <div className="security-content">
                <h4>Data Backup</h4>
                <p>Your data is automatically backed up</p>
                <small className="help-text">Last backup: Today</small>
              </div>
              <span className="status-badge active">✅ Enabled</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
