export interface ElectronAPI {
  generateUserCredentials: () => Promise<any>;
  sendEmail: (data: { email: string; userId: string; pin: string }) => Promise<any>;
  generateMachineCode: () => Promise<string>;
  storeUserData: (data: any) => Promise<any>;
  getUserData: () => Promise<any>;
  validatePin: (data: { pin: string }) => Promise<any>;
  storeRestaurantData: (data: any) => Promise<any>;
  checkSetupStatus: () => Promise<any>;
  switchToFullscreen: () => Promise<any>;
  switchToWindowed: () => Promise<any>;
  getRestaurantData: () => Promise<any>;
  getRestaurantDetails: (restaurantId: string) => Promise<any>;
  getMenuItems: (restaurantId?: string) => Promise<any[]>;
  createMenuItem: (menuItem: any) => Promise<any>;
  updateMenuItem: (id: string, updates: any) => Promise<any>;
  deleteMenuItem: (id: string) => Promise<any>;
  bulkImportMenuItems: (menuItems: any[], restaurantId: string) => Promise<any>;
  saveImage: (imageData: string, fileName: string) => Promise<any>;
  getImagePath: (relativePath: string) => Promise<string>;
  deleteImage: (relativePath: string) => Promise<any>;
  // Table Management
  getTables: (restaurantId?: string) => Promise<any[]>;
  createTable: (table: any) => Promise<any>;
  updateTable: (id: string, updates: any) => Promise<any>;
  deleteTable: (id: string) => Promise<any>;
  // Tax Management
  getTaxRates: (restaurantId?: string) => Promise<any[]>;
  createTaxRate: (taxRate: any) => Promise<any>;
  updateTaxRate: (id: string, updates: any) => Promise<any>;
  deleteTaxRate: (id: string) => Promise<any>;
  // Settings Management
  getBillingSettings: (restaurantId: string) => Promise<any>;
  saveBillingSettings: (settings: any) => Promise<any>;
  getAppSettings: (restaurantId: string) => Promise<any>;
  saveAppSettings: (settings: any) => Promise<any>;
  getAnalyticsData: (restaurantId: string, period?: string) => Promise<any>;
  // Order Management
  createOrder: (orderData: any) => Promise<any>;
  getOrders: (restaurantId: string, filters?: any) => Promise<any[]>;
  updateOrder: (orderId: string, updates: any) => Promise<any>;
  updateOrderStatus: (orderId: string, status: string) => Promise<any>;
  deleteOrder: (orderId: string) => Promise<any>;
  // Subscription Management
  getAvailablePlans: () => Promise<any[]>;
  upgradeSubscription: (userId: string, planType: string, paymentData: any) => Promise<any>;
  getPaymentHistory: (userId: string) => Promise<any[]>;
  // Auto-Updater
  updaterCheckForUpdates: () => Promise<{ success: boolean; error?: string }>;
  updaterDownloadUpdate: () => Promise<{ success: boolean; error?: string }>;
  updaterQuitAndInstall: () => Promise<{ success: boolean; error?: string }>;
  updaterGetStatus: () => Promise<{ success: boolean; status?: any; error?: string }>;
  updaterGetConfig: () => Promise<{ success: boolean; config?: any; error?: string }>;
  updaterUpdateConfig: (config: any) => Promise<{ success: boolean; error?: string }>;
  onUpdaterEvent: (callback: (event: { event: string; data?: any }) => void) => void;
  removeUpdaterEventListener: () => void;
  // Window Controls
  minimizeWindow: () => Promise<void>;
  maximizeWindow: () => Promise<void>;
  closeWindow: () => Promise<void>;
  // POS Window Management
  createPOSWindow: (restaurantId: string, userDetails: any, restaurantDetails: any) => Promise<any>;
  closePOSWindow: (windowId: string) => Promise<any>;
  closePOSWindowSelf: () => Promise<void>;
  getPOSWindows: () => Promise<string[]>;
  sendPOSMessage: (windowId: string, message: any) => Promise<any>;
  broadcastToPOSWindows: (message: any) => Promise<any>;
  // POS Window Communication
  onPOSMessage: (callback: (message: any) => void) => void;
  onPOSBroadcast: (callback: (message: any) => void) => void;
  removePOSListeners: () => void;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
