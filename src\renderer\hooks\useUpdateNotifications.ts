import { useEffect, useCallback } from 'react';
import { useNotifications } from '../contexts/NotificationContext';

interface UpdateInfo {
  version: string;
  releaseNotes?: string;
  releaseDate?: string;
}

interface UpdateStatus {
  checking: boolean;
  available: boolean;
  downloading: boolean;
  downloaded: boolean;
  error: string | null;
  progress: {
    percent: number;
    bytesPerSecond: number;
    total: number;
    transferred: number;
  } | null;
  updateInfo: UpdateInfo | null;
}

export const useUpdateNotifications = (userId: string) => {
  const { createNotification, showToast } = useNotifications();

  const handleUpdateAvailable = useCallback(async (updateInfo: UpdateInfo) => {
    // Create a notification for the update
    await createNotification({
      userId,
      type: 'system',
      title: 'Update Available',
      message: `Zyka POS v${updateInfo.version} is now available. Click to download and install.`,
      data: { updateInfo, action: 'update-available' },
      readStatus: false,
      priority: 'normal',
      autoDismiss: false,
      dismissAfter: 0,
    });

    // Show a toast notification with action
    showToast({
      type: 'info',
      title: 'Update Available',
      message: `Version ${updateInfo.version} is ready to download`,
      duration: 10000, // 10 seconds
      action: {
        label: 'Download',
        onClick: async () => {
          try {
            await window.electronAPI.updaterDownloadUpdate();
          } catch (error) {
            console.error('Failed to start update download:', error);
            showToast({
              type: 'error',
              title: 'Download Failed',
              message: 'Failed to start update download. Please try again.',
              duration: 5000,
            });
          }
        },
      },
    });
  }, [userId, createNotification, showToast]);

  const handleUpdateDownloaded = useCallback(async (updateInfo: UpdateInfo) => {
    // Create a notification for the downloaded update
    await createNotification({
      userId,
      type: 'system',
      title: 'Update Ready',
      message: `Zyka POS v${updateInfo.version} has been downloaded and is ready to install. Restart the application to apply the update.`,
      data: { updateInfo, action: 'update-downloaded' },
      readStatus: false,
      priority: 'high',
      autoDismiss: false,
      dismissAfter: 0,
    });

    // Show a toast notification with install action
    showToast({
      type: 'success',
      title: 'Update Ready',
      message: `Version ${updateInfo.version} is ready to install`,
      duration: 15000, // 15 seconds
      action: {
        label: 'Install Now',
        onClick: async () => {
          try {
            await window.electronAPI.updaterQuitAndInstall();
          } catch (error) {
            console.error('Failed to install update:', error);
            showToast({
              type: 'error',
              title: 'Installation Failed',
              message: 'Failed to install update. Please restart manually.',
              duration: 5000,
            });
          }
        },
      },
    });
  }, [userId, createNotification, showToast]);

  const handleUpdateError = useCallback(async (error: string) => {
    // Create a notification for the update error
    await createNotification({
      userId,
      type: 'system',
      title: 'Update Error',
      message: `Failed to check for updates: ${error}`,
      data: { error, action: 'update-error' },
      readStatus: false,
      priority: 'normal',
      autoDismiss: true,
      dismissAfter: 10,
    });
  }, [userId, createNotification]);

  const checkForUpdates = useCallback(async () => {
    try {
      const updateResult = await window.electronAPI.updaterCheckForUpdates();
      console.log('Update check result:', updateResult);

      // Wait a moment to get update status
      await new Promise(resolve => setTimeout(resolve, 2000));

      const statusResult = await window.electronAPI.updaterGetStatus();
      if (statusResult.success && statusResult.status.available) {
        await handleUpdateAvailable(statusResult.status.updateInfo);
      }
    } catch (error) {
      console.error('Update check failed:', error);
      await handleUpdateError((error as Error).message);
    }
  }, [handleUpdateAvailable, handleUpdateError]);

  useEffect(() => {
    // Listen for updater events
    const handleUpdaterEvent = async (event: { event: string; data?: any }) => {
      switch (event.event) {
        case 'update-available':
          await handleUpdateAvailable(event.data);
          break;
        case 'update-downloaded':
          await handleUpdateDownloaded(event.data);
          break;
        case 'update-error':
          await handleUpdateError(event.data.message);
          break;
        default:
          break;
      }
    };

    // Set up event listener for updater events
    if (window.electronAPI.onUpdaterEvent) {
      window.electronAPI.onUpdaterEvent(handleUpdaterEvent);
    }

    return () => {
      // Clean up event listener
      if (window.electronAPI.removeUpdaterEventListener) {
        window.electronAPI.removeUpdaterEventListener();
      }
    };
  }, [handleUpdateAvailable, handleUpdateDownloaded, handleUpdateError]);

  return {
    checkForUpdates,
  };
};
