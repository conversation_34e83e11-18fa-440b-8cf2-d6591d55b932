import React, { useState, useEffect } from 'react';
import Icon from '../components/Icon';

interface AnalyticsData {
  overview: {
    totalTables: number;
    totalMenuItems: number;
    totalOrders: number;
    totalRevenue: number;
    activeOrders: number;
    completedOrders: number;
    cancelledOrders: number;
  };
  todayStats: {
    ordersToday: number;
    revenueToday: number;
    averageOrderValue: number;
    busyHours: { hour: string; orders: number }[];
  };
  weeklyStats: {
    dailyRevenue: { day: string; revenue: number }[];
    popularItems: { name: string; quantity: number; revenue: number }[];
  };
  tableStats: {
    occupancyRate: number;
    averageTurnover: number;
    mostPopularTable: string;
  };
}

interface AnalyticsProps {
  restaurantId: string;
}

const Analytics: React.FC<AnalyticsProps> = ({ restaurantId }) => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month'>('today');

  useEffect(() => {
    loadAnalyticsData();
  }, [restaurantId, selectedPeriod]);

  const loadAnalyticsData = async () => {
    try {
      setIsLoading(true);

      // Load analytics data from the backend
      const data = await window.electronAPI.getAnalyticsData(restaurantId, selectedPeriod);

      if (data) {
        setAnalyticsData({
          overview: data.overview,
          todayStats: {
            ordersToday: data.periodStats.ordersInPeriod,
            revenueToday: data.periodStats.revenueInPeriod,
            averageOrderValue: data.periodStats.averageOrderValue,
            busyHours: data.periodStats.busyHours,
          },
          weeklyStats: {
            dailyRevenue: data.weeklyStats.dailyRevenue,
            popularItems: data.weeklyStats.popularItems,
          },
          tableStats: data.tableStats,
        });
      }
    } catch (error) {
      console.error('Error loading analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="analytics-page">
        <div className="analytics-loading">
          <div className="loading-spinner"></div>
          <p>Loading analytics...</p>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="analytics-page">
        <div className="analytics-error">
          <Icon name="warning" size="lg" />
          <p>Failed to load analytics data</p>
          <button className="btn btn-primary" onClick={loadAnalyticsData}>
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="analytics-page">
      <div className="analytics-header">
        <h1>Analytics Dashboard</h1>
        <div className="analytics-controls">
          <div className="period-selector">
            <button
              className={`period-btn ${selectedPeriod === 'today' ? 'active' : ''}`}
              onClick={() => setSelectedPeriod('today')}
            >
              Today
            </button>
            <button
              className={`period-btn ${selectedPeriod === 'week' ? 'active' : ''}`}
              onClick={() => setSelectedPeriod('week')}
            >
              This Week
            </button>
            <button
              className={`period-btn ${selectedPeriod === 'month' ? 'active' : ''}`}
              onClick={() => setSelectedPeriod('month')}
            >
              This Month
            </button>
          </div>
          <button
            className="btn btn-secondary refresh-btn"
            onClick={loadAnalyticsData}
            disabled={isLoading}
          >
            <Icon name="loading" size="sm" />
            {isLoading ? 'Loading...' : 'Refresh'}
          </button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="analytics-overview">
        <div className="overview-card">
          <div className="card-icon tables">
            <Icon name="grid" size="lg" />
          </div>
          <div className="card-content">
            <h3>{analyticsData.overview.totalTables}</h3>
            <p>Total Tables</p>
            <span className="card-subtitle">
              {Math.round(analyticsData.tableStats.occupancyRate)}% occupied
            </span>
          </div>
        </div>

        <div className="overview-card">
          <div className="card-icon menu">
            <Icon name="utensils" size="lg" />
          </div>
          <div className="card-content">
            <h3>{analyticsData.overview.totalMenuItems}</h3>
            <p>Menu Items</p>
            <span className="card-subtitle">Active items</span>
          </div>
        </div>

        <div className="overview-card">
          <div className="card-icon orders">
            <Icon name="shopping-cart" size="lg" />
          </div>
          <div className="card-content">
            <h3>{analyticsData.overview.totalOrders}</h3>
            <p>Total Orders</p>
            <span className="card-subtitle">{analyticsData.overview.activeOrders} active</span>
          </div>
        </div>

        <div className="overview-card">
          <div className="card-icon revenue">
            <Icon name="payment" size="lg" />
          </div>
          <div className="card-content">
            <h3>₹{analyticsData.overview.totalRevenue.toLocaleString()}</h3>
            <p>Total Revenue</p>
            <span className="card-subtitle">
              ₹{analyticsData.todayStats.revenueToday.toLocaleString()} today
            </span>
          </div>
        </div>
      </div>

      {/* Detailed Stats */}
      <div className="analytics-details">
        {/* Today's Performance */}
        <div className="analytics-section">
          <h2>Today's Performance</h2>
          <div className="performance-grid">
            <div className="performance-card">
              <h4>Orders Today</h4>
              <div className="performance-value">{analyticsData.todayStats.ordersToday}</div>
            </div>
            <div className="performance-card">
              <h4>Revenue Today</h4>
              <div className="performance-value">
                ₹{analyticsData.todayStats.revenueToday.toLocaleString()}
              </div>
            </div>
            <div className="performance-card">
              <h4>Average Order Value</h4>
              <div className="performance-value">
                ₹{Math.round(analyticsData.todayStats.averageOrderValue)}
              </div>
            </div>
          </div>
        </div>

        {/* Order Status Breakdown */}
        <div className="analytics-section">
          <h2>Order Status</h2>
          <div className="status-breakdown">
            <div className="status-item completed">
              <div className="status-count">{analyticsData.overview.completedOrders}</div>
              <div className="status-label">Completed</div>
            </div>
            <div className="status-item active">
              <div className="status-count">{analyticsData.overview.activeOrders}</div>
              <div className="status-label">Active</div>
            </div>
            <div className="status-item cancelled">
              <div className="status-count">{analyticsData.overview.cancelledOrders}</div>
              <div className="status-label">Cancelled</div>
            </div>
          </div>
        </div>

        {/* Popular Items */}
        <div className="analytics-section">
          <h2>Popular Items</h2>
          <div className="popular-items">
            {analyticsData.weeklyStats.popularItems.length > 0 ? (
              analyticsData.weeklyStats.popularItems.map((item, index) => (
                <div key={index} className="popular-item">
                  <div className="item-rank">#{index + 1}</div>
                  <div className="item-details">
                    <div className="item-name">{item.name}</div>
                    <div className="item-stats">
                      {item.quantity} orders • ₹{item.revenue.toLocaleString()}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="no-data">
                <Icon name="utensils" size="lg" />
                <p>No popular items data available for the selected period</p>
              </div>
            )}
          </div>
        </div>

        {/* Busy Hours */}
        <div className="analytics-section">
          <h2>Busy Hours Today</h2>
          <div className="busy-hours">
            {analyticsData.todayStats.busyHours.map((hour, index) => (
              <div key={index} className="busy-hour">
                <div className="hour-time">{hour.hour}</div>
                <div className="hour-bar">
                  <div
                    className="hour-fill"
                    style={{
                      width: `${(hour.orders / Math.max(...analyticsData.todayStats.busyHours.map(h => h.orders))) * 100}%`,
                    }}
                  ></div>
                </div>
                <div className="hour-count">{hour.orders}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
