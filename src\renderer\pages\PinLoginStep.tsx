import React, { useState, useEffect } from 'react';
import Button from '../components/Button';
import { UserDetails, RestaurantDetails } from '../types';

interface PinLoginStepProps {
  onComplete: () => void;
  userDetails: UserDetails;
  restaurantDetails?: RestaurantDetails;
}

const PinLoginStep: React.FC<PinLoginStepProps> = ({
  onComplete,
  userDetails,
  restaurantDetails,
}) => {
  const [pin, setPin] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handleKeypadPress = (digit: string) => {
    if (pin.length < 6) {
      setPin(prev => prev + digit);
    }
  };

  const handleBackspace = () => {
    setPin(prev => prev.slice(0, -1));
  };

  const handleClear = () => {
    setPin('');
  };

  const handleSubmitClick = () => {
    const fakeEvent = { preventDefault: () => {} } as React.FormEvent;
    handleSubmit(fakeEvent);
  };

  // Add keyboard support for PIN entry
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Only handle keyboard input if not currently loading
      if (isLoading) return;

      const key = event.key;

      // Handle number keys (0-9) from both main keyboard and numpad
      if (/^[0-9]$/.test(key) && pin.length < 6) {
        event.preventDefault();
        setPin(prev => prev + key);
        return;
      }

      // Handle backspace/delete
      if (key === 'Backspace' || key === 'Delete') {
        event.preventDefault();
        setPin(prev => prev.slice(0, -1));
        return;
      }

      // Handle Enter key to submit (if PIN is complete)
      if (key === 'Enter' && pin.length === 6) {
        event.preventDefault();
        handleSubmitClick();
        return;
      }

      // Handle Escape to clear PIN
      if (key === 'Escape') {
        event.preventDefault();
        setPin('');
        return;
      }
    };

    // Add event listener
    document.addEventListener('keydown', handleKeyPress);

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [pin, isLoading]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null);

    if (pin.length !== 6) {
      setMessage({ type: 'error', text: 'PIN must be 6 digits' });
      return;
    }

    setIsLoading(true);

    try {
      const result = await window.electronAPI.validatePin({ pin });

      if (result.valid) {
        setMessage({
          type: 'success',
          text: 'Login successful! Welcome to Zyka POS!',
        });

        // Wait a moment to show the success message
        setTimeout(() => {
          onComplete();
        }, 1500);
      } else {
        setMessage({
          type: 'error',
          text: 'Invalid PIN. Please check your email for the correct PIN.',
        });
      }
    } catch (error) {
      console.error('Error validating PIN:', error);
      setMessage({
        type: 'error',
        text: 'An error occurred. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderCompactKeypad = () => {
    const keys = [
      ['1', '2', '3'],
      ['4', '5', '6'],
      ['7', '8', '9'],
      ['clear', '0', 'backspace'],
    ];

    return (
      <div className="compact-keypad">
        {keys.flat().map(key => (
          <button
            key={key}
            type="button"
            className={`compact-keypad-btn ${key === 'clear' || key === 'backspace' ? 'keypad-action' : ''}`}
            onClick={() => {
              if (key === 'clear') handleClear();
              else if (key === 'backspace') handleBackspace();
              else handleKeypadPress(key);
            }}
            disabled={isLoading}
          >
            {key === 'backspace' ? (
              '⌫'
            ) : key === 'clear' ? (
              'Clear'
            ) : (
              key
            )}
          </button>
        ))}
      </div>
    );
  };

  return (
    <div className="compact-pin-login">
      <div className="compact-pin-login-container">
        <div className="compact-login-header">
          <h1 className="compact-app-title">Zyka POS</h1>
          <h2 className="compact-restaurant-name">{restaurantDetails?.restaurantName || 'Restaurant'}</h2>
          <p className="compact-welcome-text">Welcome back! Enter your PIN to continue</p>
        </div>

        {message && <div className={`message message-${message.type}`}>{message.text}</div>}

        <form onSubmit={handleSubmit}>
          <div className="pin-input-section">
            <div className="pin-dots-container">
              {Array.from({ length: 6 }, (_, index) => (
                <div key={index} className={`pin-dot-large ${index < pin.length ? 'filled' : 'empty'}`}>
                  {index < pin.length ? '●' : ''}
                </div>
              ))}
            </div>
          </div>

          <div className="keypad-section">
            {renderCompactKeypad()}
          </div>

          <div className="compact-login-actions">
            <Button type="button" variant="secondary" className="cancel-btn">
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              isLoading={isLoading}
              disabled={isLoading || pin.length !== 6}
              className="access-btn"
            >
              {isLoading ? 'Verifying...' : 'Login'}
            </Button>
          </div>
        </form>

        <div className="compact-login-footer">
          <p className="compact-version-info">Zyka POS v1.0.0</p>
        </div>
      </div>
    </div>
  );
};

export default PinLoginStep;
