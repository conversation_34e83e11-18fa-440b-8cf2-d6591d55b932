import React, { useState } from 'react';
import Icon from '../components/Icon';
import UserDetailsTab from '../components/settings/UserDetailsTab';
import RestaurantDetailsTab from '../components/settings/RestaurantDetailsTab';
import SystemSettingsTab from '../components/settings/SystemSettingsTab';
import SubscriptionTab from '../components/settings/SubscriptionTab';
import BillingSettingsTab from '../components/settings/BillingSettingsTab';
import TaxManagement from '../components/settings/TaxManagement';
import NotificationSettings from '../components/settings/NotificationSettings';
import { UserDetails, RestaurantDetails } from '../types';

interface SettingsProps {
  restaurantId: string;
  userDetails: UserDetails;
  restaurantDetails: RestaurantDetails;
  onUserUpdate?: (updates: Partial<UserDetails>) => void;
  onRestaurantUpdate?: (updates: Partial<RestaurantDetails>) => void;
}

type SettingsTab =
  | 'user'
  | 'restaurant'
  | 'system'
  | 'subscription'
  | 'billing'
  | 'tax'
  | 'notifications';

const Settings: React.FC<SettingsProps> = ({
  restaurantId,
  userDetails,
  restaurantDetails,
  onUserUpdate,
  onRestaurantUpdate,
}) => {
  const [activeTab, setActiveTab] = useState<SettingsTab>('user');

  const handleUserUpdate = async (updates: Partial<UserDetails>) => {
    try {
      // Update user data in database
      const result = await window.electronAPI.storeUserData({
        ...userDetails,
        ...updates,
      });

      if (result.success && onUserUpdate) {
        onUserUpdate(updates);
      }

      return result;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  };

  const handleRestaurantUpdate = async (updates: Partial<RestaurantDetails>) => {
    try {
      // Update restaurant data in database
      const result = await window.electronAPI.storeRestaurantData({
        ...restaurantDetails,
        ...updates,
      });

      if (result.success && onRestaurantUpdate) {
        onRestaurantUpdate(updates);
      }

      return result;
    } catch (error) {
      console.error('Error updating restaurant:', error);
      throw error;
    }
  };

  const tabs = [
    {
      id: 'user' as SettingsTab,
      label: 'User Details',
      icon: 'home' as const,
      description: 'Manage your personal information and account details',
    },
    {
      id: 'restaurant' as SettingsTab,
      label: 'Restaurant',
      icon: 'restaurant' as const,
      description: 'Configure restaurant information and settings',
    },
    {
      id: 'system' as SettingsTab,
      label: 'System Settings',
      icon: 'cog' as const,
      description: 'System preferences and application settings',
    },
    {
      id: 'subscription' as SettingsTab,
      label: 'Subscription',
      icon: 'key' as const,
      description: 'Manage your subscription plan and billing',
    },
    {
      id: 'billing' as SettingsTab,
      label: 'Billing Settings',
      icon: 'receipt' as const,
      description: 'Customize billing headers, footers, and printer settings',
    },
    {
      id: 'tax' as SettingsTab,
      label: 'Tax Management',
      icon: 'receipt' as const,
      description: 'Configure tax rates and manage tax settings',
    },
    {
      id: 'notifications' as SettingsTab,
      label: 'Notifications',
      icon: 'bell' as const,
      description: 'Customize notification preferences and settings',
    },
  ];

  const renderTabContent = () => {
    console.log('Rendering tab content for:', activeTab); // Debug log

    switch (activeTab) {
      case 'user':
        return <UserDetailsTab userDetails={userDetails} onUpdateUser={handleUserUpdate} />;
      case 'restaurant':
        return (
          <RestaurantDetailsTab
            restaurantDetails={restaurantDetails}
            onUpdateRestaurant={handleRestaurantUpdate}
          />
        );
      case 'system':
        return <SystemSettingsTab restaurantId={restaurantId} />;
      case 'subscription':
        return <SubscriptionTab userDetails={userDetails} />;
      case 'billing':
        return <BillingSettingsTab restaurantId={restaurantId} />;
      case 'tax':
        return <TaxManagement restaurantId={restaurantId} />;
      case 'notifications':
        return <NotificationSettings userId={userDetails.userId || restaurantId} />;
      default:
        return <UserDetailsTab userDetails={userDetails} onUpdateUser={handleUserUpdate} />;
    }
  };

  return (
    <div className="settings-page">
      <div className="settings-header">
        <h1 className="settings-title">Settings</h1>
        <p className="settings-subtitle">Configure your restaurant management system</p>
      </div>

      <div className="settings-container">
        <div className="settings-tabs-header">
          <nav className="settings-tabs-nav">
            {tabs.map(tab => (
              <button
                key={tab.id}
                className={`settings-tab-item ${activeTab === tab.id ? 'active' : ''}`}
                onClick={() => {
                  console.log('Tab clicked:', tab.id); // Debug log
                  setActiveTab(tab.id);
                }}
              >
                <div className="tab-item-icon">
                  <Icon name={tab.icon} size="md" />
                </div>
                <div className="tab-item-label">{tab.label}</div>
              </button>
            ))}
          </nav>
        </div>

        <div className="settings-content">
          <div className="settings-content-header">
            <h2 className="content-title">
              <Icon name={tabs.find(tab => tab.id === activeTab)?.icon || 'home'} size="md" />
              {tabs.find(tab => tab.id === activeTab)?.label}
            </h2>
            <p className="content-description">
              {tabs.find(tab => tab.id === activeTab)?.description}
            </p>
          </div>

          <div className="settings-content-body">{renderTabContent()}</div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
