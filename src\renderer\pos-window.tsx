import React from 'react';
import { createRoot } from 'react-dom/client';
import DedicatedPOSWindow from './components/DedicatedPOSWindow';
import { UserDetails, RestaurantDetails } from './types';
import './styles/global.css';

// Get window parameters from command line arguments
const getWindowParams = () => {
  const params = new URLSearchParams(window.location.search);

  // Try to get from URL params first (for development)
  let windowId = params.get('windowId');
  let restaurantId = params.get('restaurantId');
  let userDetails = params.get('userDetails');
  let restaurantDetails = params.get('restaurantDetails');

  // If not in URL params, try to get from process arguments (Electron)
  if (!windowId && (window as any).process?.argv) {
    const args = (window as any).process.argv;

    args.forEach((arg: string) => {
      if (arg.startsWith('--pos-window-id=')) {
        windowId = arg.split('=')[1];
      } else if (arg.startsWith('--restaurant-id=')) {
        restaurantId = arg.split('=')[1];
      } else if (arg.startsWith('--user-details=')) {
        userDetails = arg.split('=')[1];
      } else if (arg.startsWith('--restaurant-details=')) {
        restaurantDetails = arg.split('=')[1];
      }
    });
  }

  // Parse JSON strings
  let parsedUserDetails: UserDetails | null = null;
  let parsedRestaurantDetails: RestaurantDetails | null = null;

  try {
    if (userDetails) {
      parsedUserDetails = JSON.parse(decodeURIComponent(userDetails));
    }
    if (restaurantDetails) {
      parsedRestaurantDetails = JSON.parse(decodeURIComponent(restaurantDetails));
    }
  } catch (error) {
    console.error('Failed to parse window parameters:', error);
  }

  return {
    windowId: windowId || 'pos_window_' + Date.now(),
    restaurantId: restaurantId || '',
    userDetails: parsedUserDetails,
    restaurantDetails: parsedRestaurantDetails,
  };
};

const POSWindowApp: React.FC = () => {
  const { windowId, restaurantId, userDetails, restaurantDetails } = getWindowParams();

  // Show error if required parameters are missing
  if (!restaurantId || !userDetails || !restaurantDetails) {
    return (
      <div className="pos-error-container">
        <div className="pos-error-content">
          <h2>POS Window Error</h2>
          <p>Failed to load POS window parameters. Please try opening the POS window again.</p>
          <button onClick={() => window.close()} className="pos-error-close-btn">
            Close Window
          </button>
        </div>
      </div>
    );
  }

  return (
    <DedicatedPOSWindow
      windowId={windowId}
      restaurantId={restaurantId}
      userDetails={userDetails}
      restaurantDetails={restaurantDetails}
    />
  );
};

// Initialize the POS window app
const container = document.getElementById('pos-root');
if (container) {
  const root = createRoot(container);
  root.render(<POSWindowApp />);
} else {
  console.error('POS root container not found');
}
