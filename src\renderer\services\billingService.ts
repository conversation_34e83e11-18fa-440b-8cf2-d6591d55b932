import { Order, BillingSettings, RestaurantDetails, TaxRate } from '../types';
import { calculateTaxes, formatTaxBreakdown } from '../utils/taxCalculations';

export class BillingService {
  private static instance: BillingService;
  private billingSettings: BillingSettings | null = null;
  private restaurantDetails: RestaurantDetails | null = null;
  private taxRates: TaxRate[] = [];

  private constructor() {}

  public static getInstance(): BillingService {
    if (!BillingService.instance) {
      BillingService.instance = new BillingService();
    }
    return BillingService.instance;
  }

  public async loadBillingSettings(restaurantId: string): Promise<void> {
    try {
      // Load billing settings, restaurant details, and tax rates
      const [billingData, restaurantData, taxData] = await Promise.all([
        window.electronAPI?.getBillingSettings?.(restaurantId).catch(() => null),
        window.electronAPI?.getRestaurantDetails?.(restaurantId).catch(() => null),
        window.electronAPI?.getTaxRates?.(restaurantId).catch(() => []),
      ]);

      this.billingSettings = billingData || this.getDefaultSettings();
      this.restaurantDetails = restaurantData;
      this.taxRates = (taxData || []).filter((tax: TaxRate) => tax.isActive);

      // If we have restaurant details but no custom billing settings,
      // populate billing settings with restaurant details
      if (this.restaurantDetails && !billingData) {
        this.populateBillingSettingsFromRestaurant();
      }
    } catch (error) {
      console.error('Failed to load billing settings:', error);
      this.billingSettings = this.getDefaultSettings();
    }
  }

  private populateBillingSettingsFromRestaurant(): void {
    if (!this.billingSettings || !this.restaurantDetails) return;

    // Update billing settings header with restaurant details
    this.billingSettings.header = {
      ...this.billingSettings.header,
      restaurantName:
        this.restaurantDetails.restaurantName || this.billingSettings.header.restaurantName,
      address: this.restaurantDetails.restaurantAddress || this.billingSettings.header.address,
      phone: this.restaurantDetails.phone || this.billingSettings.header.phone,
      email: this.restaurantDetails.email || this.billingSettings.header.email,
      website: this.restaurantDetails.website || this.billingSettings.header.website,
      gstNumber: this.restaurantDetails.gstNumber || this.billingSettings.header.gstNumber,
    };
  }

  private getDefaultSettings(): BillingSettings {
    return {
      id: 'default',
      restaurantId: 'default',
      header: {
        showLogo: false,
        logoUrl: '',
        restaurantName: 'Restaurant Name',
        address: 'Restaurant Address',
        phone: '',
        email: '',
        website: '',
        gstNumber: '',
        customText: '',
      },
      footer: {
        thankYouMessage: 'Thank you for your visit!',
        termsAndConditions: '',
        customText: '',
        showQRCode: false,
        qrCodeData: '',
      },
      format: {
        paperSize: 'thermal_80mm',
        fontSize: 'medium',
        showItemImages: false,
        showTaxBreakdown: true,
      },
      printer: {
        printerName: '',
        autoprint: false,
        copies: 1,
      },
      updatedAt: new Date().toISOString(),
    };
  }

  public generateBillHTML(
    order: Order,
    paymentDetails?: {
      method: string;
      amountReceived?: number;
      transactionId?: string;
    }
  ): string {
    const settings = this.getCurrentBillingSettings();
    if (!settings) {
      throw new Error('Billing settings not loaded');
    }
    const timestamp = new Date().toLocaleString('en-IN');

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Bill - ${order.orderNumber}</title>
        <style>
          body { 
            font-family: 'Courier New', monospace; 
            font-size: ${settings.format.fontSize === 'small' ? '12px' : settings.format.fontSize === 'large' ? '16px' : '14px'};
            margin: 0;
            padding: 20px;
            background: white;
            color: black;
          }
          .bill { 
            max-width: ${settings.format.paperSize === 'thermal_80mm' ? '300px' : settings.format.paperSize === 'thermal_58mm' ? '200px' : '600px'};
            margin: 0 auto;
            border: 1px solid #ccc;
            padding: 20px;
          }
          .header { text-align: center; margin-bottom: 20px; }
          .logo { max-width: 100px; margin-bottom: 10px; }
          .restaurant-name { font-size: 18px; font-weight: bold; margin-bottom: 5px; }
          .address { font-size: 12px; margin-bottom: 3px; }
          .contact { font-size: 12px; margin-bottom: 3px; }
          .divider { border-top: 1px dashed #000; margin: 15px 0; }
          .order-info { margin-bottom: 15px; }
          .items { margin-bottom: 15px; }
          .item { display: flex; justify-content: space-between; margin-bottom: 5px; }
          .item-details { flex: 1; }
          .item-price { text-align: right; }
          .totals { margin-bottom: 15px; }
          .total-row { display: flex; justify-content: space-between; margin-bottom: 3px; }
          .grand-total { font-weight: bold; font-size: 16px; border-top: 1px solid #000; padding-top: 5px; }
          .payment-section { margin-bottom: 15px; }
          .footer { text-align: center; margin-top: 20px; font-size: 12px; }
          .qr-code { text-align: center; margin: 15px 0; }
          @media print {
            body { margin: 0; padding: 10px; }
            .bill { border: none; }
          }
        </style>
      </head>
      <body>
        <div class="bill">
          ${this.generateHeaderHTML(settings)}
          
          <div class="divider"></div>
          
          <div class="order-info">
            <div><strong>Order #:</strong> ${order.orderNumber}</div>
            <div><strong>Date:</strong> ${timestamp}</div>
            <div><strong>Type:</strong> ${order.orderType.toUpperCase()}</div>
            ${order.tableNumber ? `<div><strong>Table:</strong> ${order.tableNumber}</div>` : ''}
            ${order.customerName ? `<div><strong>Customer:</strong> ${order.customerName}</div>` : ''}
            ${order.customerPhone ? `<div><strong>Phone:</strong> ${order.customerPhone}</div>` : ''}
          </div>
          
          <div class="divider"></div>
          
          <div class="items">
            <div style="font-weight: bold; margin-bottom: 10px;">ITEMS:</div>
            ${order.items
              .map(
                item => `
              <div class="item">
                <div class="item-details">
                  <div>${item.menuItemName}</div>
                  <div style="font-size: 11px; color: #666;">${item.quantity} x ₹${item.menuItemPrice}</div>
                  ${item.notes ? `<div style="font-size: 10px; color: #888;">Note: ${item.notes}</div>` : ''}
                </div>
                <div class="item-price">₹${item.subtotal.toFixed(2)}</div>
              </div>
            `
              )
              .join('')}
          </div>
          
          <div class="divider"></div>
          
          <div class="totals">
            <div class="total-row">
              <span>Subtotal:</span>
              <span>₹${order.totalAmount.toFixed(2)}</span>
            </div>
            ${settings.format.showTaxBreakdown ? this.generateTaxBreakdownHTML(order) : ''}
            <div class="total-row grand-total">
              <span>TOTAL:</span>
              <span>₹${order.totalAmount.toFixed(2)}</span>
            </div>
          </div>
          
          ${paymentDetails ? this.generatePaymentHTML(paymentDetails, order.totalAmount) : ''}
          
          ${this.generateFooterHTML(settings)}
        </div>
      </body>
      </html>
    `;
  }

  private generateHeaderHTML(settings: BillingSettings): string {
    return `
      <div class="header">
        ${settings.header.showLogo && settings.header.logoUrl ? `<img src="${settings.header.logoUrl}" alt="Logo" class="logo">` : ''}
        <div class="restaurant-name">${settings.header.restaurantName || 'Restaurant Name'}</div>
        <div class="address">${settings.header.address || 'Restaurant Address'}</div>
        ${settings.header.phone ? `<div class="contact">Phone: ${settings.header.phone}</div>` : ''}
        ${settings.header.email ? `<div class="contact">Email: ${settings.header.email}</div>` : ''}
        ${settings.header.website ? `<div class="contact">Website: ${settings.header.website}</div>` : ''}
        ${settings.header.gstNumber ? `<div class="contact">GST: ${settings.header.gstNumber}</div>` : ''}
        ${settings.header.customText ? `<div class="contact">${settings.header.customText}</div>` : ''}
      </div>
    `;
  }

  private generatePaymentHTML(paymentDetails: any, totalAmount: number): string {
    const change =
      paymentDetails.method === 'cash' && paymentDetails.amountReceived
        ? Math.max(0, paymentDetails.amountReceived - totalAmount)
        : 0;

    return `
      <div class="divider"></div>
      <div class="payment-section">
        <div style="font-weight: bold; margin-bottom: 10px;">PAYMENT DETAILS:</div>
        <div class="total-row">
          <span>Method:</span>
          <span>${paymentDetails.method.toUpperCase()}</span>
        </div>
        ${
          paymentDetails.amountReceived
            ? `
          <div class="total-row">
            <span>Amount Received:</span>
            <span>₹${paymentDetails.amountReceived.toFixed(2)}</span>
          </div>
        `
            : ''
        }
        ${
          change > 0
            ? `
          <div class="total-row">
            <span>Change Given:</span>
            <span>₹${change.toFixed(2)}</span>
          </div>
        `
            : ''
        }
        ${
          paymentDetails.transactionId
            ? `
          <div class="total-row">
            <span>Transaction ID:</span>
            <span>${paymentDetails.transactionId}</span>
          </div>
        `
            : ''
        }
      </div>
    `;
  }

  private generateFooterHTML(settings: BillingSettings): string {
    return `
      <div class="divider"></div>
      <div class="footer">
        <div style="font-weight: bold; margin-bottom: 10px;">${settings.footer.thankYouMessage}</div>
        ${settings.footer.termsAndConditions ? `<div style="margin-bottom: 10px; font-size: 10px;">${settings.footer.termsAndConditions}</div>` : ''}
        ${settings.footer.customText ? `<div style="margin-bottom: 10px;">${settings.footer.customText}</div>` : ''}
        ${
          settings.footer.showQRCode && settings.footer.qrCodeData
            ? `
          <div class="qr-code">
            <div>Scan QR Code:</div>
            <div style="margin-top: 5px; font-size: 10px;">${settings.footer.qrCodeData}</div>
          </div>
        `
            : ''
        }
      </div>
    `;
  }

  public printBill(order: Order, paymentDetails?: any): void {
    try {
      const billHTML = this.generateBillHTML(order, paymentDetails);
      const currentSettings = this.getCurrentBillingSettings();

      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(billHTML);
        printWindow.document.close();

        // Auto-print if enabled
        if (currentSettings?.printer.autoprint) {
          printWindow.print();
        }
      }
    } catch (error) {
      console.error('Error printing bill:', error);
      alert('Failed to print bill. Please check your billing settings.');
    }
  }

  public getBillingSettings(): BillingSettings | null {
    return this.billingSettings;
  }

  public getCurrentBillingSettings(): BillingSettings | null {
    if (!this.billingSettings) return null;

    // Always return settings with latest restaurant details
    const currentSettings = { ...this.billingSettings };

    if (this.restaurantDetails) {
      currentSettings.header = {
        ...currentSettings.header,
        restaurantName:
          this.restaurantDetails.restaurantName || currentSettings.header.restaurantName,
        address: this.restaurantDetails.restaurantAddress || currentSettings.header.address,
        phone: this.restaurantDetails.phone || currentSettings.header.phone,
        email: this.restaurantDetails.email || currentSettings.header.email,
        website: this.restaurantDetails.website || currentSettings.header.website,
        gstNumber: this.restaurantDetails.gstNumber || currentSettings.header.gstNumber,
      };
    }

    return currentSettings;
  }

  public async refreshRestaurantDetails(restaurantId: string): Promise<void> {
    try {
      if (window.electronAPI?.getRestaurantDetails) {
        this.restaurantDetails = await window.electronAPI.getRestaurantDetails(restaurantId);
      }
    } catch (error) {
      console.error('Failed to refresh restaurant details:', error);
    }
  }

  public setRestaurantDetails(details: RestaurantDetails): void {
    this.restaurantDetails = details;
  }

  private generateTaxBreakdownHTML(order: Order): string {
    const taxCalculation = calculateTaxes(order.subtotal, this.taxRates);

    if (taxCalculation.taxBreakdown.length === 0) {
      return '';
    }

    return taxCalculation.taxBreakdown
      .map(
        tax => `
        <div class="total-row">
          <span>${tax.name} (${tax.type === 'percentage' ? `${tax.rate}%` : `₹${tax.rate}`}):</span>
          <span>₹${tax.amount.toFixed(2)}</span>
        </div>
      `
      )
      .join('');
  }
}

export const billingService = BillingService.getInstance();
