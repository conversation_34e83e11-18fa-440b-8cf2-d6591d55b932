// Global types for the application

export interface UserDetails {
  fullName: string;
  name?: string;
  address: string;
  phone: string;
  email: string;
  userId?: string;
  pin?: string;
  profileImage?: string;
  trialStartDate?: string;
  trialEndDate?: string;
  subscriptionStatus?: 'trial' | 'active' | 'expired' | 'cancelled';
  currentPlan?: 'basic' | 'premium';
  subscriptionEndDate?: string;
}

export interface RestaurantDetails {
  userId: string;
  restaurantName: string;
  restaurantAddress: string;
  restaurantType: 'Dine-In' | 'Takeaway';
  location: string;
  machineCode?: string;
  phone?: string;
  email?: string;
  website?: string;
  description?: string;
  gstNumber?: string;
}

export interface OnboardingStep {
  step: number;
  title: string;
  component: React.ComponentType<any>;
}

export interface MenuItem {
  id: string;
  name: string;
  code?: string;
  price: number;
  description?: string;
  category?: string;
  image?: string;
  available: boolean;
  restaurantId: string;
  popularity?: number;
  createdAt: string;
  updatedAt: string;
}

export interface Table {
  id: string;
  tableNumber: string;
  capacity: number;
  status: 'available' | 'occupied' | 'reserved' | 'maintenance';
  location?: string;
  restaurantId: string;
  currentOrderId?: string;
  reservationCustomerName?: string;
  reservationCustomerPhone?: string;
  reservationTime?: string;
  reservationPartySize?: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TaxRate {
  id: string;
  name: string;
  rate: number;
  type: 'percentage' | 'fixed';
  isDefault: boolean;
  isActive: boolean;
  restaurantId: string;
  createdAt: string;
  updatedAt: string;
}

export interface BillingSettings {
  id: string;
  restaurantId: string;
  header: {
    showLogo: boolean;
    logoUrl?: string;
    restaurantName: string;
    address: string;
    phone: string;
    email?: string;
    website?: string;
    gstNumber?: string;
    customText?: string;
  };
  footer: {
    thankYouMessage: string;
    termsAndConditions?: string;
    customText?: string;
    showQRCode: boolean;
    qrCodeData?: string;
  };
  format: {
    paperSize: 'A4' | 'thermal_80mm' | 'thermal_58mm';
    fontSize: 'small' | 'medium' | 'large';
    showItemImages: boolean;
    showTaxBreakdown: boolean;
  };
  printer: {
    printerName: string;
    autoprint: boolean;
    copies: number;
  };
  updatedAt: string;
}

export interface AppSettings {
  id: string;
  restaurantId: string;
  general: {
    currency: string;
    currencySymbol: string;
    language: string;
    timezone: string;
    dateFormat: string;
    timeFormat: '12h' | '24h';
  };
  pos: {
    autoSaveOrders: boolean;
    soundEnabled: boolean;
    showItemImages: boolean;
    defaultTaxRate?: string;
  };
  updatedAt: string;
}

export type NotificationType = 'orders' | 'tables' | 'bills' | 'menu' | 'tax' | 'system';
export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';
export type NotificationFrequency = 'immediate' | 'batched' | 'daily';

export interface Notification {
  id: string;
  userId: string;
  restaurantId?: number;
  type: NotificationType;
  title: string;
  message: string;
  data?: any; // JSON data for additional context
  readStatus: boolean;
  priority: NotificationPriority;
  autoDismiss: boolean;
  dismissAfter: number; // seconds, 0 means no auto-dismiss
  createdAt: string;
  readAt?: string;
}

export interface NotificationPreference {
  id: string;
  userId: string;
  category: NotificationType;
  enabled: boolean;
  soundEnabled: boolean;
  desktopEnabled: boolean;
  toastEnabled: boolean;
  autoDismissEnabled: boolean;
  autoDismissTime: number; // seconds
  frequency: NotificationFrequency;
  createdAt: string;
  updatedAt: string;
}

export interface ToastNotification {
  id: string;
  type: 'success' | 'warning' | 'error' | 'info';
  title: string;
  message: string;
  duration?: number; // milliseconds, default 5000
  action?: {
    label: string;
    onClick: () => void;
  };
}

export interface Modifier {
  id: string;
  modifierGroupId: string;
  name: string;
  price: number;
  description?: string;
  isAvailable: boolean;
  displayOrder: number;
  createdAt: string;
  updatedAt: string;
}

export interface ModifierGroup {
  id: string;
  restaurantId: string;
  name: string;
  description?: string;
  isRequired: boolean;
  minSelection: number;
  maxSelection: number;
  displayOrder: number;
  isActive: boolean;
  modifiers?: Modifier[];
  createdAt: string;
  updatedAt: string;
}

export interface MenuItemModifierGroup {
  id: string;
  menuItemId: string;
  modifierGroupId: string;
  isRequired: boolean;
  displayOrder: number;
  createdAt: string;
}

export interface SelectedModifier {
  id: string;
  name: string;
  price: number;
}

export interface OrderItem {
  id: string;
  menuItemId: string;
  menuItemName: string;
  menuItemPrice: number;
  quantity: number;
  subtotal: number;
  notes?: string;
  status: 'pending' | 'preparing' | 'ready' | 'served';
  modifiers?: SelectedModifier[];
}

export interface Order {
  id: string;
  orderNumber: string;
  restaurantId: string;
  tableId?: string;
  tableNumber?: string;
  orderType: 'dine-in' | 'takeaway' | 'delivery';
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'served' | 'completed' | 'cancelled';
  items: OrderItem[];
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  paymentMethod?: 'cash' | 'card' | 'upi' | 'other';
  customerName?: string;
  customerPhone?: string;
  notes?: string;
  kotPrinted: boolean;
  billPrinted: boolean;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface Subscription {
  id: string;
  userId: string;
  planType: 'basic' | 'premium';
  status: 'trial' | 'active' | 'expired' | 'cancelled';
  startDate: string;
  endDate: string;
  trialEndDate?: string;
  autoRenew: boolean;
  paymentMethod?: string;
  lastPaymentDate?: string;
  nextBillingDate?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentTransaction {
  id: string;
  userId: string;
  subscriptionId: string;
  amount: number;
  currency: string;
  paymentMethod: 'upi' | 'card' | 'netbanking' | 'wallet';
  transactionId?: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  gatewayResponse?: any;
  createdAt: string;
  updatedAt: string;
}

export interface LicenseFeature {
  id: string;
  name: string;
  description: string;
  planType: 'basic' | 'premium' | 'both';
  enabled: boolean;
}

export interface SubscriptionPlan {
  id: 'basic' | 'premium';
  name: string;
  price: number;
  currency: string;
  interval: string;
  features: string[];
  description: string;
  popular?: boolean;
}

export interface SubscriptionStatus {
  status: 'trial' | 'active' | 'expired' | 'cancelled';
  plan: 'basic' | 'premium' | null;
  daysRemaining: number;
  isTrialActive: boolean;
  autoRenew: boolean;
  nextBillingDate?: string;
  subscription?: Subscription;
}

export interface ElectronAPI {
  generateUserCredentials: () => Promise<{ userId: string; pin: string }>;
  sendEmail: (data: {
    email: string;
    userId: string;
    pin: string;
  }) => Promise<{ success: boolean; error?: string }>;
  generateMachineCode: () => Promise<{ machineCode: string }>;
  storeUserData: (data: any) => Promise<{ success: boolean }>;
  getUserData: () => Promise<any>;
  validatePin: (data: { pin: string }) => Promise<{ valid: boolean }>;
  storeRestaurantData: (data: any) => Promise<{ success: boolean; error?: string }>;
  checkSetupStatus: () => Promise<{ isSetupComplete: boolean; hasUsers: boolean }>;
  switchToFullscreen: () => Promise<{ success: boolean; error?: string }>;
  getRestaurantData: () => Promise<any>;
  getRestaurantDetails: (restaurantId: string) => Promise<any>;
  getMenuItems: (restaurantId?: string) => Promise<MenuItem[]>;
  createMenuItem: (
    menuItem: Omit<MenuItem, 'id' | 'createdAt' | 'updatedAt'>
  ) => Promise<{ success: boolean; menuItem?: MenuItem; error?: string }>;
  updateMenuItem: (
    id: string,
    updates: Partial<MenuItem>
  ) => Promise<{ success: boolean; menuItem?: MenuItem; error?: string }>;
  deleteMenuItem: (id: string) => Promise<{ success: boolean; error?: string }>;
  bulkImportMenuItems: (
    menuItems: any[],
    restaurantId: string
  ) => Promise<{ success: boolean; imported?: number; error?: string }>;
  saveImage: (
    imageData: string,
    fileName: string
  ) => Promise<{ success: boolean; filePath?: string; error?: string }>;
  getImagePath: (
    relativePath: string
  ) => Promise<{ success: boolean; fullPath?: string; error?: string }>;
  deleteImage: (relativePath: string) => Promise<{ success: boolean; error?: string }>;
  // Table Management
  getTables: (restaurantId?: string) => Promise<Table[]>;
  createTable: (
    table: Omit<Table, 'id' | 'createdAt' | 'updatedAt'>
  ) => Promise<{ success: boolean; table?: Table; error?: string }>;
  updateTable: (
    id: string,
    updates: Partial<Table>
  ) => Promise<{ success: boolean; table?: Table; error?: string }>;
  deleteTable: (id: string) => Promise<{ success: boolean; error?: string }>;
  // Tax Management
  getTaxRates: (restaurantId?: string) => Promise<TaxRate[]>;
  createTaxRate: (
    taxRate: Omit<TaxRate, 'id' | 'createdAt' | 'updatedAt'>
  ) => Promise<{ success: boolean; taxRate?: TaxRate; error?: string }>;
  updateTaxRate: (
    id: string,
    updates: Partial<TaxRate>
  ) => Promise<{ success: boolean; taxRate?: TaxRate; error?: string }>;
  deleteTaxRate: (id: string) => Promise<{ success: boolean; error?: string }>;
  // Settings Management
  getBillingSettings: (restaurantId: string) => Promise<BillingSettings | null>;
  saveBillingSettings: (
    settings: Omit<BillingSettings, 'id' | 'updatedAt'>
  ) => Promise<{ success: boolean; settings?: BillingSettings; error?: string }>;
  getAppSettings: (restaurantId: string) => Promise<AppSettings | null>;
  saveAppSettings: (
    settings: Omit<AppSettings, 'id' | 'updatedAt'>
  ) => Promise<{ success: boolean; settings?: AppSettings; error?: string }>;
  // Analytics
  getAnalyticsData: (restaurantId: string, period?: string) => Promise<any>;
  // Notifications
  getNotifications: (
    userId: string,
    filters?: { type?: NotificationType; unreadOnly?: boolean; limit?: number }
  ) => Promise<Notification[]>;
  createNotification: (
    notification: Omit<Notification, 'id' | 'createdAt'>
  ) => Promise<{ success: boolean; notification?: Notification; error?: string }>;
  markNotificationAsRead: (id: string) => Promise<{ success: boolean; error?: string }>;
  markAllNotificationsAsRead: (userId: string) => Promise<{ success: boolean; error?: string }>;
  deleteNotification: (id: string) => Promise<{ success: boolean; error?: string }>;
  clearAllNotifications: (userId: string) => Promise<{ success: boolean; error?: string }>;
  getUnreadNotificationCount: (userId: string) => Promise<number>;
  // Notification Preferences
  getNotificationPreferences: (userId: string) => Promise<NotificationPreference[]>;
  updateNotificationPreference: (
    userId: string,
    category: NotificationType,
    preferences: Partial<NotificationPreference>
  ) => Promise<{ success: boolean; error?: string }>;
  // Notification Events
  onNotificationReceived: (callback: (notification: Notification) => void) => void;
  removeNotificationListener: () => void;
  // Licensing and Subscription Management
  getSubscriptionStatus: (userId: string) => Promise<SubscriptionStatus>;
  checkFeatureAccess: (
    userId: string,
    featureId: string
  ) => Promise<{ hasAccess: boolean; reason: string }>;
  getAvailablePlans: () => Promise<SubscriptionPlan[]>;
  upgradeSubscription: (
    userId: string,
    planType: 'basic' | 'premium',
    paymentData: any
  ) => Promise<{
    success: boolean;
    subscription?: Subscription;
    transaction?: PaymentTransaction;
    error?: string;
  }>;
  getPaymentHistory: (userId: string) => Promise<PaymentTransaction[]>;

  // Backup Management
  createBackup: (filename?: string) => Promise<{ success: boolean; backup?: any; error?: string }>;
  getBackups: () => Promise<any[]>;
  restoreBackup: (backupId: string) => Promise<{ success: boolean; error?: string }>;
  deleteBackup: (backupId: string) => Promise<{ success: boolean; error?: string }>;
  getBackupStats: () => Promise<{
    totalBackups: number;
    totalSize: number;
    oldestBackup?: string;
    newestBackup?: string;
    lastAutomaticBackup?: string;
  }>;
  exportData: (
    format: 'json' | 'csv',
    tables?: string[]
  ) => Promise<{ success: boolean; filePath?: string; error?: string }>;

  // Database Health
  databaseHealthCheck: () => Promise<{ healthy: boolean; details: any }>;
  databaseDiagnostics: () => Promise<{ success: boolean; details: any }>;

  // Auto-Updater
  updaterCheckForUpdates: () => Promise<{ success: boolean; error?: string }>;
  updaterDownloadUpdate: () => Promise<{ success: boolean; error?: string }>;
  updaterQuitAndInstall: () => Promise<{ success: boolean; error?: string }>;
  updaterGetStatus: () => Promise<{ success: boolean; status?: any; error?: string }>;
  updaterGetConfig: () => Promise<{ success: boolean; config?: any; error?: string }>;
  updaterUpdateConfig: (config: any) => Promise<{ success: boolean; error?: string }>;
  onUpdaterEvent: (callback: (event: { event: string; data?: any }) => void) => void;
  removeUpdaterEventListener: () => void;

  // Order Management
  createOrder: (orderData: any) => Promise<{ success: boolean; order?: Order; error?: string }>;
  getOrders: (restaurantId: string, filters?: any) => Promise<Order[]>;
  updateOrder: (orderId: string, updates: any) => Promise<{ success: boolean; order?: Order; error?: string }>;
  updateOrderStatus: (orderId: string, status: string) => Promise<{ success: boolean; order?: Order; error?: string }>;
  deleteOrder: (orderId: string) => Promise<{ success: boolean; error?: string }>;

  // Window Controls
  minimizeWindow: () => Promise<void>;
  maximizeWindow: () => Promise<void>;
  closeWindow: () => Promise<void>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
