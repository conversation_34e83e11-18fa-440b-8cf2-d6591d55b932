import { TaxRate } from '../types';

export interface TaxCalculationResult {
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  taxBreakdown: Array<{
    name: string;
    rate: number;
    type: 'percentage' | 'fixed';
    amount: number;
  }>;
}

/**
 * Calculate tax amounts based on active tax rates from Tax Settings
 * @param subtotal - The subtotal amount before tax
 * @param taxRates - Array of active tax rates from Tax Settings
 * @returns Detailed tax calculation result
 */
export function calculateTaxes(subtotal: number, taxRates: TaxRate[]): TaxCalculationResult {
  if (!taxRates || taxRates.length === 0) {
    return {
      subtotal,
      taxAmount: 0,
      totalAmount: subtotal,
      taxBreakdown: [],
    };
  }

  const taxBreakdown: TaxCalculationResult['taxBreakdown'] = [];
  let totalTaxAmount = 0;

  // Calculate each tax
  for (const taxRate of taxRates.filter(tax => tax.isActive)) {
    let taxAmount = 0;

    if (taxRate.type === 'percentage') {
      taxAmount = (subtotal * taxRate.rate) / 100;
    } else if (taxRate.type === 'fixed') {
      taxAmount = taxRate.rate;
    }

    if (taxAmount > 0) {
      taxBreakdown.push({
        name: taxRate.name,
        rate: taxRate.rate,
        type: taxRate.type,
        amount: taxAmount,
      });
      totalTaxAmount += taxAmount;
    }
  }

  return {
    subtotal,
    taxAmount: totalTaxAmount,
    totalAmount: subtotal + totalTaxAmount,
    taxBreakdown,
  };
}

/**
 * Get the default tax rate from tax settings
 * @param taxRates - Array of tax rates
 * @returns Default tax rate or null if none found
 */
export function getDefaultTaxRate(taxRates: TaxRate[]): TaxRate | null {
  return taxRates.find(tax => tax.isDefault && tax.isActive) || null;
}

/**
 * Calculate tax amount using only the default tax rate (for backward compatibility)
 * @param subtotal - The subtotal amount before tax
 * @param taxRates - Array of tax rates
 * @returns Tax amount using default tax rate
 */
export function calculateDefaultTaxAmount(subtotal: number, taxRates: TaxRate[]): number {
  const defaultTax = getDefaultTaxRate(taxRates);
  if (!defaultTax) return 0;

  return defaultTax.type === 'percentage' ? (subtotal * defaultTax.rate) / 100 : defaultTax.rate;
}

/**
 * Format tax breakdown for display
 * @param taxBreakdown - Tax breakdown array
 * @returns Formatted string for display
 */
export function formatTaxBreakdown(taxBreakdown: TaxCalculationResult['taxBreakdown']): string {
  return taxBreakdown.map(tax => `${tax.name}: ₹${tax.amount.toFixed(2)}`).join(', ');
}

/**
 * Calculate service charge if applicable
 * @param subtotal - The subtotal amount
 * @param serviceChargeRate - Service charge rate (percentage or fixed)
 * @param serviceChargeType - Type of service charge ('percentage' or 'fixed')
 * @returns Service charge amount
 */
export function calculateServiceCharge(
  subtotal: number,
  serviceChargeRate: number,
  serviceChargeType: 'percentage' | 'fixed'
): number {
  if (serviceChargeRate <= 0) return 0;

  return serviceChargeType === 'percentage'
    ? (subtotal * serviceChargeRate) / 100
    : serviceChargeRate;
}

/**
 * Calculate discount amount
 * @param subtotal - The subtotal amount
 * @param discountValue - Discount value
 * @param discountType - Type of discount ('percentage' or 'amount')
 * @returns Discount amount
 */
export function calculateDiscountAmount(
  subtotal: number,
  discountValue: number,
  discountType: 'percentage' | 'amount'
): number {
  if (discountValue <= 0) return 0;

  if (discountType === 'percentage') {
    return Math.min((subtotal * discountValue) / 100, subtotal);
  } else {
    return Math.min(discountValue, subtotal);
  }
}
