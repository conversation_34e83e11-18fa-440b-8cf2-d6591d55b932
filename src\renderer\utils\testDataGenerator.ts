import { MenuItem, Table } from '../types';

// Generate test menu items for performance testing
export const generateTestMenuItems = (
  count: number = 100
): Omit<MenuItem, 'id' | 'createdAt' | 'updatedAt'>[] => {
  const categories = [
    'Appetizers',
    'Soups',
    'Salads',
    'Main Course',
    'Pasta',
    'Pizza',
    'Seafood',
    'Grilled Items',
    'Vegetarian',
    'Desserts',
    'Beverages',
    'Specials',
  ];

  const itemNames = [
    'Crispy',
    'Grilled',
    'Roasted',
    'Steamed',
    'Fried',
    'Baked',
    'Sautéed',
    'Marinated',
    'Spicy',
    'Mild',
    'Tangy',
    'Sweet',
    'Savory',
    'Fresh',
    'Organic',
    'Premium',
  ];

  const ingredients = [
    'Chicken',
    'Beef',
    'Fish',
    'Shrimp',
    'Vegetables',
    'Mushrooms',
    'Cheese',
    'Pasta',
    'Rice',
    'Noodles',
    'Tofu',
    'Paneer',
    'Lamb',
    'Pork',
    'Salmon',
    'Tuna',
  ];

  const preparations = [
    'with Herbs',
    'in Sauce',
    'with Spices',
    'Curry Style',
    'Mediterranean Style',
    'Asian Style',
    'Italian Style',
    'Mexican Style',
    'Indian Style',
    'Continental',
  ];

  const items: Omit<MenuItem, 'id' | 'createdAt' | 'updatedAt'>[] = [];

  for (let i = 0; i < count; i++) {
    const category = categories[Math.floor(Math.random() * categories.length)];
    const name = itemNames[Math.floor(Math.random() * itemNames.length)];
    const ingredient = ingredients[Math.floor(Math.random() * ingredients.length)];
    const preparation = preparations[Math.floor(Math.random() * preparations.length)];

    const fullName = `${name} ${ingredient} ${preparation}`;
    const price = Math.floor(Math.random() * 500) + 50; // ₹50 to ₹550

    items.push({
      name: fullName,
      description: `Delicious ${name.toLowerCase()} ${ingredient.toLowerCase()} prepared ${preparation.toLowerCase()}. A popular choice among our customers.`,
      price,
      category,
      available: Math.random() > 0.1, // 90% available
      image: '', // No image for test data
      restaurantId: 'test-restaurant',
    });
  }

  return items;
};

// Generate test tables for performance testing
export const generateTestTables = (
  count: number = 50
): Omit<Table, 'id' | 'createdAt' | 'updatedAt'>[] => {
  const areas = [
    'Main Hall',
    'Garden Area',
    'VIP Section',
    'Terrace',
    'Private Dining',
    'Bar Area',
  ];
  const statuses: Array<'available' | 'occupied' | 'reserved' | 'maintenance'> = [
    'available',
    'occupied',
    'reserved',
    'maintenance',
  ];
  const capacities = [2, 4, 6, 8, 10, 12];

  const tables: Omit<Table, 'id' | 'createdAt' | 'updatedAt'>[] = [];

  for (let i = 1; i <= count; i++) {
    const area = areas[Math.floor(Math.random() * areas.length)];
    const capacity = capacities[Math.floor(Math.random() * capacities.length)];

    // Most tables should be available for testing
    let status: 'available' | 'occupied' | 'reserved' | 'maintenance';
    const rand = Math.random();
    if (rand < 0.7) status = 'available';
    else if (rand < 0.85) status = 'occupied';
    else if (rand < 0.95) status = 'reserved';
    else status = 'maintenance';

    tables.push({
      tableNumber: i.toString(),
      capacity,
      status,
      location: area,
      restaurantId: 'test-restaurant',
      isActive: true,
    });
  }

  return tables;
};

// Performance testing utilities
export const measurePerformance = (name: string, fn: () => void) => {
  const start = performance.now();
  fn();
  const end = performance.now();
  console.log(`${name} took ${end - start} milliseconds`);
  return end - start;
};

export const measureAsyncPerformance = async (name: string, fn: () => Promise<void>) => {
  const start = performance.now();
  await fn();
  const end = performance.now();
  console.log(`${name} took ${end - start} milliseconds`);
  return end - start;
};

// Test scenarios for performance validation
export const performanceTestScenarios = {
  // Test menu item filtering with large dataset
  testMenuFiltering: (menuItems: MenuItem[]) => {
    console.log('Testing menu filtering performance...');

    measurePerformance('Filter by category', () => {
      const filtered = menuItems.filter(item => item.category === 'Main Course');
      console.log(`Filtered ${filtered.length} items from ${menuItems.length} total`);
    });

    measurePerformance('Search by name', () => {
      const searched = menuItems.filter(item => item.name.toLowerCase().includes('chicken'));
      console.log(`Found ${searched.length} items matching search`);
    });

    measurePerformance('Complex filter (available + category + search)', () => {
      const complex = menuItems.filter(
        item =>
          item.available &&
          item.category === 'Main Course' &&
          item.name.toLowerCase().includes('grilled')
      );
      console.log(`Complex filter returned ${complex.length} items`);
    });
  },

  // Test table operations with large dataset
  testTableOperations: (tables: Table[]) => {
    console.log('Testing table operations performance...');

    measurePerformance('Filter available tables', () => {
      const available = tables.filter(table => table.status === 'available');
      console.log(`Found ${available.length} available tables from ${tables.length} total`);
    });

    measurePerformance('Group tables by area', () => {
      const grouped = tables.reduce(
        (groups, table) => {
          const area = table.location || 'Main Area';
          if (!groups[area]) groups[area] = [];
          groups[area].push(table);
          return groups;
        },
        {} as Record<string, Table[]>
      );
      console.log(`Grouped tables into ${Object.keys(grouped).length} areas`);
    });
  },

  // Test virtualization performance
  testVirtualization: (itemCount: number) => {
    console.log('Testing virtualization performance...');

    const containerHeight = 600;
    const itemHeight = 200;
    const visibleItems = Math.ceil(containerHeight / itemHeight) + 2; // Buffer

    measurePerformance('Calculate visible range', () => {
      const scrollTop = Math.random() * (itemCount * itemHeight);
      const startIndex = Math.floor(scrollTop / itemHeight);
      const endIndex = Math.min(itemCount - 1, startIndex + visibleItems);
      console.log(
        `Visible range: ${startIndex} to ${endIndex} (${endIndex - startIndex + 1} items)`
      );
    });
  },
};

// Utility to run all performance tests
export const runPerformanceTests = (menuItems: MenuItem[], tables: Table[]) => {
  console.log('=== POS Performance Test Suite ===');
  console.log(`Testing with ${menuItems.length} menu items and ${tables.length} tables`);

  performanceTestScenarios.testMenuFiltering(menuItems);
  performanceTestScenarios.testTableOperations(tables);
  performanceTestScenarios.testVirtualization(menuItems.length);

  console.log('=== Performance Tests Complete ===');
};
