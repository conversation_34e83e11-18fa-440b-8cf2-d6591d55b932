import axios from 'axios';
import * as cron from 'node-cron';
import { Low } from 'lowdb';

// Analytics configuration interface
export interface AnalyticsConfig {
  measurementId: string;
  apiSecret: string;
  enabled: boolean;
  syncInterval: string; // cron expression
  lastSyncTimestamp?: string;
}

// Event interfaces for Google Analytics 4
export interface GAEvent {
  name: string;
  params: Record<string, any>;
}

export interface GAPayload {
  client_id: string;
  events: GAEvent[];
}

// Analytics service class
export class AnalyticsService {
  private config: AnalyticsConfig;
  private db: Low<any>;
  private syncJob?: cron.ScheduledTask;
  private readonly GA4_ENDPOINT = 'https://www.google-analytics.com/mp/collect';

  constructor(db: Low<any>, config: AnalyticsConfig) {
    this.db = db;
    this.config = config;
  }

  // Initialize the analytics service
  async initialize(): Promise<void> {
    if (!this.config.enabled) {
      console.log('Analytics service disabled - no credentials provided');
      return;
    }

    if (!this.config.measurementId || !this.config.apiSecret) {
      console.log('Analytics service disabled - missing GA4 credentials');
      return;
    }

    console.log('Initializing Analytics Service for software owner monitoring...');
    console.log(`Sync interval: ${this.config.syncInterval}`);

    // Start periodic sync
    this.startPeriodicSync();

    // Perform initial sync after a short delay to allow app to fully initialize
    setTimeout(async () => {
      await this.performSync();
    }, 30000); // 30 second delay
  }

  // Start periodic data synchronization
  private startPeriodicSync(): void {
    if (this.syncJob) {
      this.syncJob.stop();
    }

    this.syncJob = cron.schedule(this.config.syncInterval, async () => {
      console.log('Running scheduled analytics sync...');
      await this.performSync();
    });

    // The job starts automatically, no need to call start()
    console.log(`Analytics sync scheduled: ${this.config.syncInterval}`);
  }

  // Perform data synchronization
  async performSync(): Promise<{ success: boolean; error?: string; syncedRecords?: number }> {
    try {
      await this.db.read();

      const lastSync = this.config.lastSyncTimestamp
        ? new Date(this.config.lastSyncTimestamp)
        : new Date(0);
      const currentSync = new Date();

      let totalSynced = 0;

      // Sync users
      const userEvents = await this.generateUserEvents(lastSync);
      totalSynced += await this.sendEvents(userEvents);

      // Sync restaurants
      const restaurantEvents = await this.generateRestaurantEvents(lastSync);
      totalSynced += await this.sendEvents(restaurantEvents);

      // Sync subscriptions
      const subscriptionEvents = await this.generateSubscriptionEvents(lastSync);
      totalSynced += await this.sendEvents(subscriptionEvents);

      // Sync payment transactions
      const paymentEvents = await this.generatePaymentEvents(lastSync);
      totalSynced += await this.sendEvents(paymentEvents);

      // Update last sync timestamp
      this.config.lastSyncTimestamp = currentSync.toISOString();
      await this.saveConfig();

      console.log(`Analytics sync completed. Synced ${totalSynced} events.`);
      return { success: true, syncedRecords: totalSynced };
    } catch (error) {
      console.error('Analytics sync failed:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  // Generate user-related events
  private async generateUserEvents(lastSync: Date): Promise<GAEvent[]> {
    const events: GAEvent[] = [];
    const users = this.db.data.users || [];

    for (const user of users) {
      const createdAt = new Date(user.createdAt);

      // User registration event
      if (createdAt > lastSync) {
        events.push({
          name: 'user_registered',
          params: {
            user_id: user.userId,
            subscription_status: user.subscriptionStatus,
            current_plan: user.currentPlan || 'none',
            trial_start_date: user.trialStartDate,
            trial_end_date: user.trialEndDate,
            registration_date: user.createdAt,
            user_type: 'restaurant_owner',
            user_email: user.email, // For software owner to identify users
            user_name: user.fullName,
            user_phone: user.phone,
            user_address: user.address,
          },
        });
      }

      // Trial started event
      if (user.trialStartDate && new Date(user.trialStartDate) > lastSync) {
        events.push({
          name: 'trial_started',
          params: {
            user_id: user.userId,
            trial_start_date: user.trialStartDate,
            trial_end_date: user.trialEndDate,
            plan_type: user.currentPlan || 'trial',
          },
        });
      }

      // Subscription status changes
      if (user.subscriptionStatus === 'active' && user.subscriptionEndDate) {
        const subEndDate = new Date(user.subscriptionEndDate);
        if (subEndDate > lastSync) {
          events.push({
            name: 'subscription_activated',
            params: {
              user_id: user.userId,
              plan_type: user.currentPlan,
              subscription_end_date: user.subscriptionEndDate,
              subscription_status: user.subscriptionStatus,
            },
          });
        }
      }
    }

    return events;
  }

  // Generate restaurant-related events
  private async generateRestaurantEvents(lastSync: Date): Promise<GAEvent[]> {
    const events: GAEvent[] = [];
    const restaurants = this.db.data.restaurants || [];

    for (const restaurant of restaurants) {
      const createdAt = new Date(restaurant.createdAt);

      if (createdAt > lastSync) {
        events.push({
          name: 'restaurant_created',
          params: {
            user_id: restaurant.userId,
            restaurant_name: restaurant.restaurantName,
            restaurant_type: restaurant.restaurantType,
            location: restaurant.location,
            machine_code: restaurant.machineCode,
            creation_date: restaurant.createdAt,
          },
        });
      }
    }

    return events;
  }

  // Generate subscription-related events
  private async generateSubscriptionEvents(lastSync: Date): Promise<GAEvent[]> {
    const events: GAEvent[] = [];
    const subscriptions = this.db.data.subscriptions || [];

    for (const subscription of subscriptions) {
      const createdAt = new Date(subscription.createdAt);
      const updatedAt = new Date(subscription.updatedAt);

      // New subscription
      if (createdAt > lastSync) {
        events.push({
          name: 'subscription_created',
          params: {
            user_id: subscription.userId,
            subscription_id: subscription.id,
            plan_type: subscription.planType,
            status: subscription.status,
            start_date: subscription.startDate,
            end_date: subscription.endDate,
            auto_renew: subscription.autoRenew,
            payment_method: subscription.paymentMethod,
          },
        });
      }

      // Subscription updates
      if (updatedAt > lastSync && updatedAt > createdAt) {
        events.push({
          name: 'subscription_updated',
          params: {
            user_id: subscription.userId,
            subscription_id: subscription.id,
            plan_type: subscription.planType,
            status: subscription.status,
            auto_renew: subscription.autoRenew,
            last_payment_date: subscription.lastPaymentDate,
            next_billing_date: subscription.nextBillingDate,
          },
        });
      }
    }

    return events;
  }

  // Generate payment-related events
  private async generatePaymentEvents(lastSync: Date): Promise<GAEvent[]> {
    const events: GAEvent[] = [];
    const payments = this.db.data.paymentTransactions || [];

    for (const payment of payments) {
      const createdAt = new Date(payment.createdAt);

      if (createdAt > lastSync) {
        events.push({
          name: 'payment_processed',
          params: {
            user_id: payment.userId,
            subscription_id: payment.subscriptionId,
            transaction_id: payment.id,
            amount: payment.amount,
            currency: payment.currency,
            payment_method: payment.paymentMethod,
            status: payment.status,
            gateway_transaction_id: payment.transactionId,
            processing_date: payment.createdAt,
          },
        });

        // Revenue event for successful payments
        if (payment.status === 'completed') {
          events.push({
            name: 'purchase',
            params: {
              user_id: payment.userId,
              transaction_id: payment.id,
              value: payment.amount,
              currency: payment.currency,
              payment_method: payment.paymentMethod,
              item_category: 'subscription',
            },
          });
        }
      }
    }

    return events;
  }

  // Send events to Google Analytics 4
  private async sendEvents(events: GAEvent[]): Promise<number> {
    if (events.length === 0) return 0;

    try {
      // Split events into batches of 25 (GA4 limit)
      const batchSize = 25;
      let sentCount = 0;

      for (let i = 0; i < events.length; i += batchSize) {
        const batch = events.slice(i, i + batchSize);

        const payload: GAPayload = {
          client_id: this.generateClientId(),
          events: batch,
        };

        const response = await axios.post(
          `${this.GA4_ENDPOINT}?measurement_id=${this.config.measurementId}&api_secret=${this.config.apiSecret}`,
          payload,
          {
            headers: {
              'Content-Type': 'application/json',
            },
            timeout: 10000,
          }
        );

        if (response.status === 204) {
          sentCount += batch.length;
          console.log(`Sent batch of ${batch.length} events to GA4`);
        } else {
          console.error(`GA4 API returned status: ${response.status}`);
        }

        // Small delay between batches to avoid rate limiting
        if (i + batchSize < events.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      return sentCount;
    } catch (error) {
      console.error('Failed to send events to GA4:', error);
      throw error;
    }
  }

  // Generate a consistent client ID for the application
  private generateClientId(): string {
    // Use a combination of machine-specific data to create a consistent client ID
    const os = require('os');
    const crypto = require('crypto');

    const machineId = `${os.hostname()}-${os.platform()}-${os.arch()}`;
    return crypto.createHash('md5').update(machineId).digest('hex');
  }

  // Save configuration
  private async saveConfig(): Promise<void> {
    // This will be implemented when we add config management
    console.log('Config saved (placeholder)');
  }

  // Update configuration
  async updateConfig(newConfig: Partial<AnalyticsConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };

    if (newConfig.syncInterval && this.syncJob) {
      this.startPeriodicSync();
    }

    await this.saveConfig();
  }

  // Stop the service
  stop(): void {
    if (this.syncJob) {
      this.syncJob.stop();
      console.log('Analytics service stopped');
    }
  }

  // Get sync status
  getSyncStatus(): { enabled: boolean; lastSync?: string; nextSync?: string } {
    return {
      enabled: this.config.enabled,
      lastSync: this.config.lastSyncTimestamp,
      nextSync: this.syncJob ? 'Scheduled' : 'Not scheduled',
    };
  }
}
