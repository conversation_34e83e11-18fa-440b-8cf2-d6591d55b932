import { autoUpdater, UpdateInfo } from 'electron-updater';
import { app, BrowserWindow, dialog } from 'electron';
import * as path from 'path';
import * as fs from 'fs';

export interface UpdateConfig {
  enabled: boolean;
  checkOnStartup: boolean;
  checkInterval: number; // in hours
  allowPrerelease: boolean;
  autoDownload: boolean;
  autoInstallOnAppQuit: boolean;
  notifyUser: boolean;
}

export interface UpdateStatus {
  checking: boolean;
  available: boolean;
  downloading: boolean;
  downloaded: boolean;
  error: string | null;
  progress: {
    percent: number;
    bytesPerSecond: number;
    total: number;
    transferred: number;
  } | null;
  updateInfo: UpdateInfo | null;
}

export class AutoUpdaterService {
  private config: UpdateConfig;
  private status: UpdateStatus;
  private configPath: string;
  private checkTimer: NodeJS.Timeout | null = null;
  private mainWindow: BrowserWindow | null = null;

  constructor() {
    this.configPath = path.join(app.getPath('userData'), 'updater-config.json');
    this.config = this.getDefaultConfig();
    this.status = this.getDefaultStatus();

    this.setupAutoUpdater();
    this.loadConfig();
    this.configureUpdateServer();
  }

  private getDefaultConfig(): UpdateConfig {
    return {
      enabled: true,
      checkOnStartup: true,
      checkInterval: 24, // Check every 24 hours
      allowPrerelease: false,
      autoDownload: true,
      autoInstallOnAppQuit: true,
      notifyUser: true
    };
  }

  private getDefaultStatus(): UpdateStatus {
    return {
      checking: false,
      available: false,
      downloading: false,
      downloaded: false,
      error: null,
      progress: null,
      updateInfo: null
    };
  }

  private configureUpdateServer(): void {
    // Configure update server based on environment
    if (process.env.NODE_ENV === 'production') {
      // Use Cloudflare Pages for production
      autoUpdater.setFeedURL({
        provider: 'generic',
        url: 'https://zyka-pos-updates.pages.dev'
      });
      console.log('Auto-updater configured for Cloudflare Pages');
    } else {
      // Use GitHub releases for development/testing
      console.log('Auto-updater configured for GitHub releases');
    }
  }

  private setupAutoUpdater(): void {
    // Configure auto-updater
    autoUpdater.autoDownload = this.config.autoDownload;
    autoUpdater.autoInstallOnAppQuit = this.config.autoInstallOnAppQuit;
    autoUpdater.allowPrerelease = this.config.allowPrerelease;

    // Set up event listeners
    autoUpdater.on('checking-for-update', () => {
      console.log('Checking for update...');
      this.status.checking = true;
      this.status.error = null;
      this.notifyRenderer('update-checking');
    });

    autoUpdater.on('update-available', (info: UpdateInfo) => {
      console.log('Update available:', info);
      this.status.checking = false;
      this.status.available = true;
      this.status.updateInfo = info;
      this.notifyRenderer('update-available', info);
      
      if (this.config.notifyUser) {
        this.showUpdateAvailableDialog(info);
      }
    });

    autoUpdater.on('update-not-available', (info: UpdateInfo) => {
      console.log('Update not available:', info);
      this.status.checking = false;
      this.status.available = false;
      this.status.updateInfo = info;
      this.notifyRenderer('update-not-available', info);
    });

    autoUpdater.on('error', (error) => {
      console.error('Update error:', error);
      this.status.checking = false;
      this.status.downloading = false;
      this.status.error = error.message;
      this.notifyRenderer('update-error', error);
    });

    autoUpdater.on('download-progress', (progress) => {
      console.log('Download progress:', progress);
      this.status.downloading = true;
      this.status.progress = progress;
      this.notifyRenderer('update-download-progress', progress);
    });

    autoUpdater.on('update-downloaded', (info: UpdateInfo) => {
      console.log('Update downloaded:', info);
      this.status.downloading = false;
      this.status.downloaded = true;
      this.status.updateInfo = info;
      this.notifyRenderer('update-downloaded', info);
      
      if (this.config.notifyUser) {
        this.showUpdateDownloadedDialog(info);
      }
    });
  }

  private loadConfig(): void {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8');
        const loadedConfig = JSON.parse(configData);
        this.config = { ...this.config, ...loadedConfig };
      } else {
        this.saveConfig();
      }
    } catch (error) {
      console.error('Failed to load updater config:', error);
    }
  }

  private saveConfig(): void {
    try {
      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
    } catch (error) {
      console.error('Failed to save updater config:', error);
    }
  }

  private notifyRenderer(event: string, data?: any): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('updater-event', { event, data });
    }
  }

  private async showUpdateAvailableDialog(info: UpdateInfo): Promise<void> {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) return;

    const response = await dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: 'Update Available',
      message: `A new version (${info.version}) is available!`,
      detail: `Current version: ${app.getVersion()}\nNew version: ${info.version}\n\nWould you like to download it now?`,
      buttons: ['Download Now', 'Later', 'Skip This Version'],
      defaultId: 0,
      cancelId: 1
    });

    if (response.response === 0) {
      // Download now
      this.downloadUpdate();
    } else if (response.response === 2) {
      // Skip this version
      autoUpdater.allowPrerelease = false;
    }
  }

  private async showUpdateDownloadedDialog(info: UpdateInfo): Promise<void> {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) return;

    const response = await dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: 'Update Ready',
      message: `Update ${info.version} has been downloaded and is ready to install.`,
      detail: 'The application will restart to apply the update.',
      buttons: ['Restart Now', 'Later'],
      defaultId: 0,
      cancelId: 1
    });

    if (response.response === 0) {
      // Restart now
      this.quitAndInstall();
    }
  }

  // Public methods
  async initialize(mainWindow: BrowserWindow): Promise<void> {
    this.mainWindow = mainWindow;
    
    if (this.config.enabled && this.config.checkOnStartup) {
      // Check for updates on startup (with a small delay)
      setTimeout(() => {
        this.checkForUpdates();
      }, 5000);
    }

    if (this.config.enabled && this.config.checkInterval > 0) {
      this.startPeriodicCheck();
    }
  }

  async checkForUpdates(): Promise<void> {
    if (!this.config.enabled) {
      console.log('Auto-updater is disabled');
      return;
    }

    try {
      await autoUpdater.checkForUpdatesAndNotify();
    } catch (error) {
      console.error('Failed to check for updates:', error);
      this.status.error = (error as Error).message;
    }
  }

  async downloadUpdate(): Promise<void> {
    if (!this.status.available) {
      throw new Error('No update available to download');
    }

    try {
      await autoUpdater.downloadUpdate();
    } catch (error) {
      console.error('Failed to download update:', error);
      this.status.error = (error as Error).message;
      throw error;
    }
  }

  quitAndInstall(): void {
    if (!this.status.downloaded) {
      throw new Error('No update downloaded to install');
    }

    autoUpdater.quitAndInstall();
  }

  private startPeriodicCheck(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
    }

    const intervalMs = this.config.checkInterval * 60 * 60 * 1000; // Convert hours to milliseconds
    this.checkTimer = setInterval(() => {
      this.checkForUpdates();
    }, intervalMs);
  }

  updateConfig(newConfig: Partial<UpdateConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();

    // Update auto-updater settings
    autoUpdater.autoDownload = this.config.autoDownload;
    autoUpdater.autoInstallOnAppQuit = this.config.autoInstallOnAppQuit;
    autoUpdater.allowPrerelease = this.config.allowPrerelease;

    // Restart periodic check if interval changed
    if (newConfig.checkInterval !== undefined) {
      if (this.config.enabled && this.config.checkInterval > 0) {
        this.startPeriodicCheck();
      } else if (this.checkTimer) {
        clearInterval(this.checkTimer);
        this.checkTimer = null;
      }
    }
  }

  getConfig(): UpdateConfig {
    return { ...this.config };
  }

  getStatus(): UpdateStatus {
    return { ...this.status };
  }

  stop(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = null;
    }
  }
}

// Singleton instance
let autoUpdaterServiceInstance: AutoUpdaterService | null = null;

export function getAutoUpdaterService(): AutoUpdaterService {
  if (!autoUpdaterServiceInstance) {
    autoUpdaterServiceInstance = new AutoUpdaterService();
  }
  return autoUpdaterServiceInstance;
}
