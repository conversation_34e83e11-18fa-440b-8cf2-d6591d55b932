import * as cron from 'node-cron';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { app } from 'electron';
import { getDatabaseService } from './databaseService';
import { notificationService } from './notificationService';

export interface BackupConfig {
  enabled: boolean;
  frequency: 'daily' | 'weekly' | 'monthly';
  maxBackups: number;
  compressionEnabled: boolean;
  cloudSync: boolean;
  customPath?: string;
}

export interface BackupInfo {
  id: string;
  filename: string;
  filePath: string;
  size: number;
  type: 'manual' | 'automatic' | 'scheduled';
  status: 'completed' | 'failed' | 'in_progress';
  checksum: string;
  metadata: {
    version: string;
    recordCounts: Record<string, number>;
    compressionRatio?: number;
  };
  createdAt: string;
}

export class BackupService {
  private databaseService = getDatabaseService();
  private config: BackupConfig;
  private scheduledJob?: cron.ScheduledTask;
  private backupPath: string;

  constructor(config?: Partial<BackupConfig>) {
    const userDataPath = app.getPath('userData');
    this.backupPath = path.join(userDataPath, 'backups');

    this.config = {
      enabled: true,
      frequency: 'daily',
      maxBackups: 7,
      compressionEnabled: true,
      cloudSync: false,
      ...config,
    };

    // Ensure backup directory exists
    if (!fs.existsSync(this.backupPath)) {
      fs.mkdirSync(this.backupPath, { recursive: true });
    }
  }

  async initialize(): Promise<void> {
    await this.databaseService.initialize();

    if (this.config.enabled) {
      this.scheduleBackups();
    }
  }

  private scheduleBackups(): void {
    if (this.scheduledJob) {
      this.scheduledJob.stop();
    }

    let cronExpression: string;
    switch (this.config.frequency) {
      case 'daily':
        cronExpression = '0 2 * * *'; // 2 AM daily
        break;
      case 'weekly':
        cronExpression = '0 2 * * 0'; // 2 AM every Sunday
        break;
      case 'monthly':
        cronExpression = '0 2 1 * *'; // 2 AM on 1st of every month
        break;
      default:
        cronExpression = '0 2 * * *';
    }

    this.scheduledJob = cron.schedule(
      cronExpression,
      async () => {
        console.log('Running scheduled backup...');
        await this.createAutomaticBackup();
      },
      {
        timezone: 'UTC',
      }
    );

    console.log(`Backup scheduled: ${this.config.frequency} at 2 AM UTC`);
  }

  async createManualBackup(
    filename?: string
  ): Promise<{ success: boolean; backup?: BackupInfo; error?: string }> {
    try {
      console.log('Creating manual backup...');

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFilename = filename || `manual-backup-${timestamp}.db`;

      const result = await this.databaseService.createBackup(backupFilename);

      if (!result.success) {
        return { success: false, error: result.error };
      }

      const backupInfo = await this.getBackupInfo(result.filename!);

      // Clean up old backups if needed
      await this.cleanupOldBackups();

      // Send notification for successful backup
      await this.notifyBackupStatus(true, backupInfo.filename);

      console.log('Manual backup created successfully:', backupInfo.filename);
      return { success: true, backup: backupInfo };
    } catch (error) {
      console.error('Manual backup failed:', error);
      // Send notification for failed backup
      await this.notifyBackupStatus(false);
      return { success: false, error: (error as Error).message };
    }
  }

  async createAutomaticBackup(): Promise<{
    success: boolean;
    backup?: BackupInfo;
    error?: string;
  }> {
    try {
      console.log('Creating automatic backup...');

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFilename = `auto-backup-${timestamp}.db`;

      const result = await this.databaseService.createBackup(backupFilename);

      if (!result.success) {
        return { success: false, error: result.error };
      }

      const backupInfo = await this.getBackupInfo(result.filename!);

      // Clean up old backups
      await this.cleanupOldBackups();

      // Send notification for successful automatic backup
      await this.notifyBackupStatus(true, backupInfo.filename);

      console.log('Automatic backup created successfully:', backupInfo.filename);
      return { success: true, backup: backupInfo };
    } catch (error) {
      console.error('Automatic backup failed:', error);
      // Send notification for failed automatic backup
      await this.notifyBackupStatus(false);
      return { success: false, error: (error as Error).message };
    }
  }

  async getBackups(): Promise<BackupInfo[]> {
    try {
      const backups = await this.databaseService.getBackups();
      return backups.map(backup => ({
        id: backup.id,
        filename: backup.filename,
        filePath: backup.file_path,
        size: backup.size,
        type: backup.type,
        status: backup.status,
        checksum: backup.checksum,
        metadata: JSON.parse(backup.metadata || '{}'),
        createdAt: backup.created_at,
      }));
    } catch (error) {
      console.error('Error getting backups:', error);
      return [];
    }
  }

  async restoreBackup(backupId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const backups = await this.getBackups();
      const backup = backups.find(b => b.id === backupId);

      if (!backup) {
        return { success: false, error: 'Backup not found' };
      }

      if (!fs.existsSync(backup.filePath)) {
        return { success: false, error: 'Backup file not found on disk' };
      }

      // Verify backup integrity
      const isValid = await this.verifyBackupIntegrity(backup);
      if (!isValid) {
        return { success: false, error: 'Backup file is corrupted' };
      }

      // Create a backup of current database before restore
      await this.createManualBackup(
        `pre-restore-backup-${new Date().toISOString().replace(/[:.]/g, '-')}.db`
      );

      // Restore the backup
      const result = await this.databaseService.restoreBackup(backup.filePath);

      if (result.success) {
        console.log('Backup restored successfully:', backup.filename);
      }

      return result;
    } catch (error) {
      console.error('Restore failed:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  async deleteBackup(backupId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const backups = await this.getBackups();
      const backup = backups.find(b => b.id === backupId);

      if (!backup) {
        return { success: false, error: 'Backup not found' };
      }

      // Delete file from disk
      if (fs.existsSync(backup.filePath)) {
        fs.unlinkSync(backup.filePath);
      }

      // Remove from database (assuming we have a method to delete backup records)
      // This would need to be implemented in the database service

      console.log('Backup deleted successfully:', backup.filename);
      return { success: true };
    } catch (error) {
      console.error('Delete backup failed:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  async exportData(
    format: 'json' | 'csv',
    tables?: string[]
  ): Promise<{ success: boolean; filePath?: string; error?: string }> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `data-export-${timestamp}.${format}`;
      const exportPath = path.join(this.backupPath, filename);

      if (format === 'json') {
        await this.exportToJSON(exportPath, tables);
      } else if (format === 'csv') {
        await this.exportToCSV(exportPath, tables);
      }

      return { success: true, filePath: exportPath };
    } catch (error) {
      console.error('Data export failed:', error);
      return { success: false, error: (error as Error).message };
    }
  }

  private async exportToJSON(filePath: string, tables?: string[]): Promise<void> {
    // Implementation would depend on your specific needs
    // This is a placeholder for JSON export functionality
    const exportData = {
      exportedAt: new Date().toISOString(),
      version: '1.0',
      data: {},
    };

    fs.writeFileSync(filePath, JSON.stringify(exportData, null, 2));
  }

  private async exportToCSV(filePath: string, tables?: string[]): Promise<void> {
    // Implementation would depend on your specific needs
    // This is a placeholder for CSV export functionality
    const csvContent = 'Export functionality not yet implemented\n';
    fs.writeFileSync(filePath, csvContent);
  }

  private async getBackupInfo(filename: string): Promise<BackupInfo> {
    const filePath = path.join(this.backupPath, filename);
    const stats = fs.statSync(filePath);
    const checksum = crypto.createHash('md5').update(fs.readFileSync(filePath)).digest('hex');

    return {
      id: `backup_${Date.now()}`,
      filename,
      filePath,
      size: stats.size,
      type: 'manual',
      status: 'completed',
      checksum,
      metadata: {
        version: '1.0',
        recordCounts: {},
      },
      createdAt: new Date().toISOString(),
    };
  }

  private async verifyBackupIntegrity(backup: BackupInfo): Promise<boolean> {
    try {
      if (!fs.existsSync(backup.filePath)) {
        return false;
      }

      const currentChecksum = crypto
        .createHash('md5')
        .update(fs.readFileSync(backup.filePath))
        .digest('hex');
      return currentChecksum === backup.checksum;
    } catch (error) {
      console.error('Backup integrity check failed:', error);
      return false;
    }
  }

  private async cleanupOldBackups(): Promise<void> {
    try {
      const backups = await this.getBackups();

      // Sort by creation date, newest first
      const sortedBackups = backups.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      // Keep only the configured number of backups
      const backupsToDelete = sortedBackups.slice(this.config.maxBackups);

      for (const backup of backupsToDelete) {
        await this.deleteBackup(backup.id);
      }

      if (backupsToDelete.length > 0) {
        console.log(`Cleaned up ${backupsToDelete.length} old backups`);
      }
    } catch (error) {
      console.error('Backup cleanup failed:', error);
    }
  }

  updateConfig(newConfig: Partial<BackupConfig>): void {
    this.config = { ...this.config, ...newConfig };

    if (this.config.enabled) {
      this.scheduleBackups();
    } else if (this.scheduledJob) {
      this.scheduledJob.stop();
      this.scheduledJob = undefined;
    }
  }

  getConfig(): BackupConfig {
    return { ...this.config };
  }

  async getBackupStats(): Promise<{
    totalBackups: number;
    totalSize: number;
    oldestBackup?: string;
    newestBackup?: string;
    lastAutomaticBackup?: string;
  }> {
    try {
      const backups = await this.getBackups();

      if (backups.length === 0) {
        return { totalBackups: 0, totalSize: 0 };
      }

      const totalSize = backups.reduce((sum, backup) => sum + backup.size, 0);
      const sortedBackups = backups.sort(
        (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );

      const automaticBackups = backups.filter(b => b.type === 'automatic');
      const lastAutoBackup =
        automaticBackups.length > 0
          ? automaticBackups.sort(
              (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
            )[0]
          : undefined;

      return {
        totalBackups: backups.length,
        totalSize,
        oldestBackup: sortedBackups[0]?.createdAt,
        newestBackup: sortedBackups[sortedBackups.length - 1]?.createdAt,
        lastAutomaticBackup: lastAutoBackup?.createdAt,
      };
    } catch (error) {
      console.error('Error getting backup stats:', error);
      return { totalBackups: 0, totalSize: 0 };
    }
  }

  stop(): void {
    if (this.scheduledJob) {
      this.scheduledJob.stop();
      this.scheduledJob = undefined;
    }
  }

  private async notifyBackupStatus(success: boolean, filename?: string): Promise<void> {
    try {
      // Get the current user ID from settings
      const currentUser = await this.databaseService.sqliteService.get(
        'SELECT value FROM settings WHERE key = ?',
        ['current_user_id']
      );

      if (currentUser?.value) {
        await notificationService.notifyBackupStatus(currentUser.value, success, filename);
      }
    } catch (error) {
      console.error('Error sending backup notification:', error);
    }
  }
}

// Singleton instance
let backupService: BackupService | null = null;

export function getBackupService(): BackupService {
  if (!backupService) {
    backupService = new BackupService();
  }
  return backupService;
}
