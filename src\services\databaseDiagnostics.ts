import { app } from 'electron';
import * as path from 'path';
import * as fs from 'fs';
import * as sqlite3 from 'sqlite3';

export interface DatabaseDiagnosticResult {
  success: boolean;
  details: {
    userDataPath?: string;
    userDataExists?: boolean;
    userDataWritable?: boolean;
    databasePath?: string;
    databaseExists?: boolean;
    databaseWritable?: boolean;
    sqlite3ModuleLoaded?: boolean;
    sqlite3Version?: string;
    connectionTest?: boolean;
    error?: string;
  };
}

export class DatabaseDiagnostics {
  static async runDiagnostics(): Promise<DatabaseDiagnosticResult> {
    const result: DatabaseDiagnosticResult = {
      success: false,
      details: {}
    };

    try {
      // Check user data path
      const userDataPath = app.getPath('userData');
      result.details.userDataPath = userDataPath;
      result.details.userDataExists = fs.existsSync(userDataPath);

      // Test user data directory writability
      try {
        if (!result.details.userDataExists) {
          fs.mkdirSync(userDataPath, { recursive: true });
        }
        const testFile = path.join(userDataPath, 'diagnostic-test.tmp');
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
        result.details.userDataWritable = true;
      } catch (error) {
        result.details.userDataWritable = false;
        result.details.error = `User data directory not writable: ${(error as Error).message}`;
      }

      // Check database path
      const databasePath = path.join(userDataPath, 'zyka.db');
      result.details.databasePath = databasePath;
      result.details.databaseExists = fs.existsSync(databasePath);

      // Test database file writability
      try {
        if (result.details.databaseExists) {
          // Test existing database file
          fs.accessSync(databasePath, fs.constants.R_OK | fs.constants.W_OK);
          result.details.databaseWritable = true;
        } else {
          // Test if we can create database file
          const testDb = path.join(userDataPath, 'diagnostic-db-test.db');
          fs.writeFileSync(testDb, '');
          fs.unlinkSync(testDb);
          result.details.databaseWritable = true;
        }
      } catch (error) {
        result.details.databaseWritable = false;
        if (!result.details.error) {
          result.details.error = `Database file not writable: ${(error as Error).message}`;
        }
      }

      // Check SQLite3 module
      try {
        result.details.sqlite3ModuleLoaded = !!(sqlite3 && typeof sqlite3.Database === 'function');
        if (result.details.sqlite3ModuleLoaded) {
          result.details.sqlite3Version = sqlite3.VERSION;
        }
      } catch (error) {
        result.details.sqlite3ModuleLoaded = false;
        if (!result.details.error) {
          result.details.error = `SQLite3 module error: ${(error as Error).message}`;
        }
      }

      // Test database connection
      if (result.details.sqlite3ModuleLoaded && result.details.userDataWritable) {
        try {
          await this.testDatabaseConnection(databasePath);
          result.details.connectionTest = true;
        } catch (error) {
          result.details.connectionTest = false;
          if (!result.details.error) {
            result.details.error = `Database connection test failed: ${(error as Error).message}`;
          }
        }
      }

      // Determine overall success
      result.success = !!(
        result.details.userDataExists &&
        result.details.userDataWritable &&
        result.details.databaseWritable &&
        result.details.sqlite3ModuleLoaded &&
        result.details.connectionTest
      );

    } catch (error) {
      result.success = false;
      result.details.error = `Diagnostic failed: ${(error as Error).message}`;
    }

    return result;
  }

  private static testDatabaseConnection(dbPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const testDb = new sqlite3.Database(':memory:', (err) => {
        if (err) {
          reject(err);
          return;
        }

        testDb.run('CREATE TABLE test (id INTEGER)', (createErr) => {
          if (createErr) {
            testDb.close();
            reject(createErr);
            return;
          }

          testDb.run('INSERT INTO test (id) VALUES (1)', (insertErr) => {
            if (insertErr) {
              testDb.close();
              reject(insertErr);
              return;
            }

            testDb.get('SELECT id FROM test WHERE id = 1', (selectErr, row: any) => {
              testDb.close();
              if (selectErr) {
                reject(selectErr);
                return;
              }
              if (!row || row.id !== 1) {
                reject(new Error('Database test query failed'));
                return;
              }
              resolve();
            });
          });
        });
      });
    });
  }

  static async logDiagnostics(): Promise<void> {
    console.log('=== DATABASE DIAGNOSTICS ===');
    const result = await this.runDiagnostics();
    
    console.log('Overall Success:', result.success);
    console.log('User Data Path:', result.details.userDataPath);
    console.log('User Data Exists:', result.details.userDataExists);
    console.log('User Data Writable:', result.details.userDataWritable);
    console.log('Database Path:', result.details.databasePath);
    console.log('Database Exists:', result.details.databaseExists);
    console.log('Database Writable:', result.details.databaseWritable);
    console.log('SQLite3 Module Loaded:', result.details.sqlite3ModuleLoaded);
    console.log('SQLite3 Version:', result.details.sqlite3Version);
    console.log('Connection Test:', result.details.connectionTest);
    
    if (result.details.error) {
      console.error('Diagnostic Error:', result.details.error);
    }
    
    console.log('=== END DIAGNOSTICS ===');
  }
}
