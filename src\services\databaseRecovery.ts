import { app } from 'electron';
import * as path from 'path';
import * as fs from 'fs';
import * as sqlite3 from 'sqlite3';

export interface RecoveryResult {
  success: boolean;
  action: 'none' | 'recreated' | 'restored' | 'fallback';
  message: string;
  error?: string;
}

export class DatabaseRecovery {
  /**
   * Attempt to recover from database initialization failures
   */
  static async attemptRecovery(originalError: Error, dbPath: string): Promise<RecoveryResult> {
    console.log('Attempting database recovery for error:', originalError.message);

    // Try different recovery strategies in order
    const strategies = [
      () => this.tryDatabaseRepair(dbPath),
      () => this.tryBackupRestore(dbPath),
      () => this.tryDatabaseRecreation(dbPath),
      () => this.tryFallbackLocation(dbPath)
    ];

    for (const strategy of strategies) {
      try {
        const result = await strategy();
        if (result.success) {
          console.log('Database recovery successful:', result.message);
          return result;
        }
        console.log('Recovery strategy failed:', result.message);
      } catch (error) {
        console.log('Recovery strategy error:', (error as Error).message);
      }
    }

    return {
      success: false,
      action: 'none',
      message: 'All recovery strategies failed',
      error: originalError.message
    };
  }

  /**
   * Try to repair the existing database
   */
  private static async tryDatabaseRepair(dbPath: string): Promise<RecoveryResult> {
    if (!fs.existsSync(dbPath)) {
      return {
        success: false,
        action: 'none',
        message: 'Database file does not exist, cannot repair'
      };
    }

    return new Promise((resolve) => {
      const db = new sqlite3.Database(dbPath, sqlite3.OPEN_READWRITE, (err) => {
        if (err) {
          resolve({
            success: false,
            action: 'none',
            message: 'Cannot open database for repair',
            error: err.message
          });
          return;
        }

        // Try to run PRAGMA integrity_check
        db.get('PRAGMA integrity_check', (integrityErr, result) => {
          if (integrityErr) {
            db.close();
            resolve({
              success: false,
              action: 'none',
              message: 'Database integrity check failed',
              error: integrityErr.message
            });
            return;
          }

          // If integrity check passes, try a simple query
          db.get('SELECT name FROM sqlite_master WHERE type="table" LIMIT 1', (queryErr) => {
            db.close();
            if (queryErr) {
              resolve({
                success: false,
                action: 'none',
                message: 'Database query test failed',
                error: queryErr.message
              });
            } else {
              resolve({
                success: true,
                action: 'none',
                message: 'Database repair successful - database is functional'
              });
            }
          });
        });
      });
    });
  }

  /**
   * Try to restore from a backup
   */
  private static async tryBackupRestore(dbPath: string): Promise<RecoveryResult> {
    const userDataPath = app.getPath('userData');
    const backupDir = path.join(userDataPath, 'backups');

    if (!fs.existsSync(backupDir)) {
      return {
        success: false,
        action: 'none',
        message: 'No backup directory found'
      };
    }

    // Find the most recent backup
    const backupFiles = fs.readdirSync(backupDir)
      .filter(file => file.endsWith('.db') || file.endsWith('.sqlite'))
      .map(file => ({
        name: file,
        path: path.join(backupDir, file),
        stats: fs.statSync(path.join(backupDir, file))
      }))
      .sort((a, b) => b.stats.mtime.getTime() - a.stats.mtime.getTime());

    if (backupFiles.length === 0) {
      return {
        success: false,
        action: 'none',
        message: 'No backup files found'
      };
    }

    const latestBackup = backupFiles[0];

    try {
      // Create backup of corrupted database
      if (fs.existsSync(dbPath)) {
        const corruptedBackup = `${dbPath}.corrupted.${Date.now()}`;
        fs.copyFileSync(dbPath, corruptedBackup);
      }

      // Restore from backup
      fs.copyFileSync(latestBackup.path, dbPath);

      return {
        success: true,
        action: 'restored',
        message: `Database restored from backup: ${latestBackup.name}`
      };
    } catch (error) {
      return {
        success: false,
        action: 'none',
        message: 'Failed to restore from backup',
        error: (error as Error).message
      };
    }
  }

  /**
   * Try to recreate the database from scratch
   */
  private static async tryDatabaseRecreation(dbPath: string): Promise<RecoveryResult> {
    try {
      // Backup corrupted database if it exists
      if (fs.existsSync(dbPath)) {
        const backupPath = `${dbPath}.corrupted.${Date.now()}`;
        fs.copyFileSync(dbPath, backupPath);
        fs.unlinkSync(dbPath);
      }

      // Test if we can create a new database
      return new Promise((resolve) => {
        const testDb = new sqlite3.Database(dbPath, sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE, (err) => {
          if (err) {
            resolve({
              success: false,
              action: 'none',
              message: 'Cannot create new database',
              error: err.message
            });
            return;
          }

          // Test basic functionality
          testDb.run('CREATE TABLE test (id INTEGER)', (createErr) => {
            if (createErr) {
              testDb.close();
              resolve({
                success: false,
                action: 'none',
                message: 'Cannot create tables in new database',
                error: createErr.message
              });
              return;
            }

            testDb.run('DROP TABLE test', (dropErr) => {
              testDb.close();
              if (dropErr) {
                resolve({
                  success: false,
                  action: 'none',
                  message: 'Database functionality test failed',
                  error: dropErr.message
                });
              } else {
                resolve({
                  success: true,
                  action: 'recreated',
                  message: 'Database recreated successfully'
                });
              }
            });
          });
        });
      });
    } catch (error) {
      return {
        success: false,
        action: 'none',
        message: 'Database recreation failed',
        error: (error as Error).message
      };
    }
  }

  /**
   * Try using a fallback location for the database
   */
  private static async tryFallbackLocation(originalDbPath: string): Promise<RecoveryResult> {
    const userDataPath = app.getPath('userData');
    const fallbackPath = path.join(userDataPath, 'fallback', 'zyka.db');

    try {
      // Ensure fallback directory exists
      const fallbackDir = path.dirname(fallbackPath);
      if (!fs.existsSync(fallbackDir)) {
        fs.mkdirSync(fallbackDir, { recursive: true });
      }

      // Test fallback location
      return new Promise((resolve) => {
        const testDb = new sqlite3.Database(fallbackPath, sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE, (err) => {
          if (err) {
            resolve({
              success: false,
              action: 'none',
              message: 'Fallback location also failed',
              error: err.message
            });
            return;
          }

          testDb.close();
          resolve({
            success: true,
            action: 'fallback',
            message: `Using fallback database location: ${fallbackPath}`
          });
        });
      });
    } catch (error) {
      return {
        success: false,
        action: 'none',
        message: 'Fallback location failed',
        error: (error as Error).message
      };
    }
  }
}
