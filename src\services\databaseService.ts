import { getSQLiteService } from './sqliteService';

// Enhanced interfaces for better type safety
export interface User {
  id?: number;
  userId: string;
  pin: string;
  fullName: string;
  address: string;
  phone: string;
  email: string;
  trialStartDate?: string;
  trialEndDate?: string;
  subscriptionStatus: 'trial' | 'active' | 'expired' | 'cancelled';
  currentPlan?: 'basic' | 'premium';
  subscriptionEndDate?: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Restaurant {
  id?: number;
  userId: string;
  restaurantName: string;
  restaurantAddress: string;
  restaurantType: string;
  location: string;
  machineCode: string;
  phone?: string;
  email?: string;
  website?: string;
  description?: string;
  gstNumber?: string;
  currency: string;
  currencySymbol: string;
  timezone: string;
  taxRate: number;
  serviceCharge: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface MenuItem {
  id: string;
  restaurantId: string;
  name: string;
  code?: string;
  price: number;
  description?: string;
  category?: string;
  image?: string;
  available: boolean;
  isDeleted: boolean;
  calories?: number;
  protein?: number;
  carbs?: number;
  fat?: number;
  allergens?: string[];
  preparationTime: number;
  popularity: number;
  createdAt: string;
  updatedAt: string;
}

export interface Modifier {
  id: string;
  modifierGroupId: string;
  name: string;
  price: number;
  description?: string;
  isAvailable: boolean;
  displayOrder: number;
  createdAt: string;
  updatedAt: string;
}

export interface ModifierGroup {
  id: string;
  restaurantId: string;
  name: string;
  description?: string;
  isRequired: boolean;
  minSelection: number;
  maxSelection: number;
  displayOrder: number;
  isActive: boolean;
  modifiers?: Modifier[];
  createdAt: string;
  updatedAt: string;
}

export interface MenuItemModifierGroup {
  id: string;
  menuItemId: string;
  modifierGroupId: string;
  isRequired: boolean;
  displayOrder: number;
  createdAt: string;
}

export interface OrderItem {
  id: string;
  orderId: string;
  menuItemId: string;
  menuItemName: string;
  menuItemPrice: number;
  quantity: number;
  subtotal: number;
  notes?: string;
  status: 'pending' | 'preparing' | 'ready' | 'served';
  modifiers?: { id: string; name: string; price: number }[];
  preparationStartTime?: string;
  preparationEndTime?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Order {
  id: string;
  orderNumber: string;
  restaurantId: string;
  tableId?: string;
  tableNumber?: string;
  orderType: 'dine-in' | 'takeaway' | 'delivery';
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'served' | 'completed' | 'cancelled';
  subtotal: number;
  taxAmount: number;
  serviceChargeAmount: number;
  discountAmount: number;
  totalAmount: number;
  paymentStatus: 'pending' | 'paid' | 'refunded' | 'partially_paid';
  paymentMethod?: 'cash' | 'card' | 'upi' | 'other';
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  notes?: string;
  kotPrinted: boolean;
  billPrinted: boolean;
  orderSource: 'pos' | 'online' | 'phone' | 'walk-in';
  staffId?: string;
  deviceId?: string;
  sessionId?: string;
  estimatedDeliveryTime?: string;
  actualDeliveryTime?: string;
  rating?: number;
  feedback?: string;
  items: OrderItem[];
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  cancelledAt?: string;
}

export interface Table {
  id: string;
  restaurantId: string;
  tableNumber: string;
  capacity: number;
  location?: string;
  status: 'available' | 'occupied' | 'reserved' | 'maintenance';
  currentOrderId?: string;
  reservationCustomerName?: string;
  reservationCustomerPhone?: string;
  reservationTime?: string;
  reservationPartySize?: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export class DatabaseService {
  public sqliteService = getSQLiteService();
  private initialized = false;

  async initialize(): Promise<void> {
    if (this.initialized) return;

    console.log('Initializing fresh SQLite database...');
    await this.sqliteService.initialize();

    // Create default license features
    await this.createDefaultLicenseFeatures();

    this.initialized = true;
    console.log('SQLite database initialized successfully');
  }

  private async createDefaultLicenseFeatures(): Promise<void> {
    try {
      // Check if license features already exist
      const existingFeatures = await this.sqliteService.get(
        'SELECT COUNT(*) as count FROM settings WHERE key = ?',
        ['license_features_created']
      );

      if (existingFeatures?.count > 0) {
        return; // Already created
      }

      // Create default license features in settings
      const licenseFeatures = [
        {
          id: 'ordering',
          name: 'Order Management',
          description: 'Create and manage customer orders',
          planType: 'both',
          enabled: true
        },
        {
          id: 'kot',
          name: 'Kitchen Order Tickets',
          description: 'Print KOT for kitchen staff',
          planType: 'both',
          enabled: true
        },
        {
          id: 'menu_management',
          name: 'Advanced Menu Management',
          description: 'Full menu customization and categories',
          planType: 'premium',
          enabled: true
        },
        {
          id: 'table_management',
          name: 'Table Management',
          description: 'Manage restaurant tables and reservations',
          planType: 'premium',
          enabled: true
        },
        {
          id: 'analytics',
          name: 'Analytics & Reports',
          description: 'Detailed sales reports and analytics',
          planType: 'premium',
          enabled: true
        },
        {
          id: 'inventory',
          name: 'Inventory Management',
          description: 'Track stock and inventory levels',
          planType: 'premium',
          enabled: true
        }
      ];

      await this.sqliteService.run(
        'INSERT INTO settings (key, value) VALUES (?, ?)',
        ['license_features', JSON.stringify(licenseFeatures)]
      );

      await this.sqliteService.run(
        'INSERT INTO settings (key, value) VALUES (?, ?)',
        ['license_features_created', '1']
      );

      console.log('Default license features created');
    } catch (error) {
      console.error('Error creating default license features:', error);
    }
  }

  // User management
  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const now = new Date().toISOString();
    const result = await this.sqliteService.run(
      `INSERT INTO users (
        user_id, pin, full_name, address, phone, email,
        trial_start_date, trial_end_date, subscription_status,
        current_plan, subscription_end_date, is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        userData.userId,
        userData.pin,
        userData.fullName,
        userData.address,
        userData.phone,
        userData.email,
        userData.trialStartDate || null,
        userData.trialEndDate || null,
        userData.subscriptionStatus,
        userData.currentPlan || null,
        userData.subscriptionEndDate || null,
        userData.isActive ? 1 : 0,
        now,
        now
      ]
    );

    return this.getUserById(userData.userId);
  }

  async getUserById(userId: string): Promise<User> {
    const user = await this.sqliteService.get<any>(
      'SELECT * FROM users WHERE user_id = ?',
      [userId]
    );
    
    if (!user) throw new Error('User not found');
    
    return this.mapUserFromDb(user);
  }

  async getUserByEmail(email: string): Promise<User | null> {
    const user = await this.sqliteService.get<any>(
      'SELECT * FROM users WHERE email = ?',
      [email]
    );
    
    return user ? this.mapUserFromDb(user) : null;
  }

  async updateUser(userId: string, updates: Partial<User>): Promise<User> {
    const setClause = Object.keys(updates)
      .filter(key => key !== 'id' && key !== 'userId' && key !== 'createdAt')
      .map(key => `${this.camelToSnake(key)} = ?`)
      .join(', ');
    
    const values = Object.keys(updates)
      .filter(key => key !== 'id' && key !== 'userId' && key !== 'createdAt')
      .map(key => updates[key as keyof User]);
    
    values.push(new Date().toISOString(), userId);

    await this.sqliteService.run(
      `UPDATE users SET ${setClause}, updated_at = ? WHERE user_id = ?`,
      values
    );

    return this.getUserById(userId);
  }

  // Restaurant management
  async createRestaurant(restaurantData: Omit<Restaurant, 'id' | 'createdAt' | 'updatedAt'>): Promise<Restaurant> {
    const now = new Date().toISOString();
    await this.sqliteService.run(
      `INSERT INTO restaurants (
        user_id, restaurant_name, restaurant_address, restaurant_type,
        location, machine_code, phone, email, website, description, gst_number,
        currency, currency_symbol, timezone, tax_rate, service_charge,
        is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        restaurantData.userId,
        restaurantData.restaurantName,
        restaurantData.restaurantAddress,
        restaurantData.restaurantType,
        restaurantData.location,
        restaurantData.machineCode,
        restaurantData.phone || null,
        restaurantData.email || null,
        restaurantData.website || null,
        restaurantData.description || null,
        restaurantData.gstNumber || null,
        restaurantData.currency,
        restaurantData.currencySymbol,
        restaurantData.timezone,
        restaurantData.taxRate,
        restaurantData.serviceCharge,
        restaurantData.isActive ? 1 : 0,
        now,
        now
      ]
    );

    return this.getRestaurantByUserId(restaurantData.userId);
  }

  async getRestaurantByUserId(userId: string): Promise<Restaurant> {
    const restaurant = await this.sqliteService.get<any>(
      'SELECT * FROM restaurants WHERE user_id = ? AND is_active = 1',
      [userId]
    );
    
    if (!restaurant) throw new Error('Restaurant not found');
    
    return this.mapRestaurantFromDb(restaurant);
  }

  // Menu management
  async createMenuItem(menuItem: Omit<MenuItem, 'createdAt' | 'updatedAt'>): Promise<MenuItem> {
    const now = new Date().toISOString();
    await this.sqliteService.run(
      `INSERT INTO menu_items (
        id, restaurant_id, name, code, price, description, category, image,
        available, is_deleted, calories, protein, carbs, fat,
        allergens, preparation_time, popularity, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        menuItem.id,
        menuItem.restaurantId,
        menuItem.name,
        menuItem.code || null,
        menuItem.price,
        menuItem.description || null,
        menuItem.category || null,
        menuItem.image || null,
        menuItem.available ? 1 : 0,
        menuItem.isDeleted ? 1 : 0,
        menuItem.calories || null,
        menuItem.protein || null,
        menuItem.carbs || null,
        menuItem.fat || null,
        menuItem.allergens ? JSON.stringify(menuItem.allergens) : null,
        menuItem.preparationTime,
        menuItem.popularity,
        now,
        now
      ]
    );

    return this.getMenuItemById(menuItem.id);
  }

  async getMenuItemById(id: string): Promise<MenuItem> {
    const item = await this.sqliteService.get<any>(
      'SELECT * FROM menu_items WHERE id = ? AND is_deleted = 0',
      [id]
    );
    
    if (!item) throw new Error('Menu item not found');
    
    return this.mapMenuItemFromDb(item);
  }

  async getMenuItemsByRestaurant(restaurantId: string, availableOnly = false): Promise<MenuItem[]> {
    let sql = 'SELECT * FROM menu_items WHERE restaurant_id = ? AND is_deleted = 0';
    const params = [restaurantId];
    
    if (availableOnly) {
      sql += ' AND available = 1';
    }
    
    sql += ' ORDER BY category, name';
    
    const items = await this.sqliteService.all<any>(sql, params);
    return items.map(item => this.mapMenuItemFromDb(item));
  }

  // Helper methods for mapping database rows to objects
  private mapUserFromDb(dbUser: any): User {
    return {
      id: dbUser.id,
      userId: dbUser.user_id,
      pin: dbUser.pin,
      fullName: dbUser.full_name,
      address: dbUser.address,
      phone: dbUser.phone,
      email: dbUser.email,
      trialStartDate: dbUser.trial_start_date,
      trialEndDate: dbUser.trial_end_date,
      subscriptionStatus: dbUser.subscription_status,
      currentPlan: dbUser.current_plan,
      subscriptionEndDate: dbUser.subscription_end_date,
      isActive: dbUser.is_active === 1,
      lastLoginAt: dbUser.last_login_at,
      createdAt: dbUser.created_at,
      updatedAt: dbUser.updated_at
    };
  }

  private mapRestaurantFromDb(dbRestaurant: any): Restaurant {
    return {
      id: dbRestaurant.id,
      userId: dbRestaurant.user_id,
      restaurantName: dbRestaurant.restaurant_name,
      restaurantAddress: dbRestaurant.restaurant_address,
      restaurantType: dbRestaurant.restaurant_type,
      location: dbRestaurant.location,
      machineCode: dbRestaurant.machine_code,
      phone: dbRestaurant.phone,
      email: dbRestaurant.email,
      website: dbRestaurant.website,
      description: dbRestaurant.description,
      currency: dbRestaurant.currency,
      currencySymbol: dbRestaurant.currency_symbol,
      timezone: dbRestaurant.timezone,
      taxRate: dbRestaurant.tax_rate,
      serviceCharge: dbRestaurant.service_charge,
      isActive: dbRestaurant.is_active === 1,
      createdAt: dbRestaurant.created_at,
      updatedAt: dbRestaurant.updated_at
    };
  }

  private mapMenuItemFromDb(dbItem: any): MenuItem {
    return {
      id: dbItem.id,
      restaurantId: dbItem.restaurant_id,
      name: dbItem.name,
      code: dbItem.code,
      price: dbItem.price,
      description: dbItem.description,
      category: dbItem.category,
      image: dbItem.image,
      available: dbItem.available === 1,
      isDeleted: dbItem.is_deleted === 1,
      calories: dbItem.calories,
      protein: dbItem.protein,
      carbs: dbItem.carbs,
      fat: dbItem.fat,
      allergens: dbItem.allergens ? JSON.parse(dbItem.allergens) : [],
      preparationTime: dbItem.preparation_time,
      popularity: dbItem.popularity,
      createdAt: dbItem.created_at,
      updatedAt: dbItem.updated_at
    };
  }

  private camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  // Modifier Group Management
  async createModifierGroup(modifierGroup: Omit<ModifierGroup, 'createdAt' | 'updatedAt'>): Promise<ModifierGroup> {
    const now = new Date().toISOString();
    await this.sqliteService.run(
      `INSERT INTO modifier_groups (
        id, restaurant_id, name, description, is_required, min_selection,
        max_selection, display_order, is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        modifierGroup.id,
        modifierGroup.restaurantId,
        modifierGroup.name,
        modifierGroup.description || null,
        modifierGroup.isRequired ? 1 : 0,
        modifierGroup.minSelection,
        modifierGroup.maxSelection,
        modifierGroup.displayOrder,
        modifierGroup.isActive ? 1 : 0,
        now,
        now
      ]
    );

    return this.getModifierGroupById(modifierGroup.id);
  }

  async getModifierGroups(restaurantId: string): Promise<ModifierGroup[]> {
    const rows = await this.sqliteService.all(
      `SELECT * FROM modifier_groups
       WHERE restaurant_id = ? AND is_active = 1
       ORDER BY display_order ASC, name ASC`,
      [restaurantId]
    );

    const groups = rows.map(this.mapModifierGroupFromDb);

    // Load modifiers for each group
    for (const group of groups) {
      group.modifiers = await this.getModifiersByGroupId(group.id);
    }

    return groups;
  }

  async getModifierGroupById(id: string): Promise<ModifierGroup> {
    const row = await this.sqliteService.get(
      'SELECT * FROM modifier_groups WHERE id = ?',
      [id]
    );

    if (!row) {
      throw new Error('Modifier group not found');
    }

    const group = this.mapModifierGroupFromDb(row);
    group.modifiers = await this.getModifiersByGroupId(id);
    return group;
  }

  async updateModifierGroup(id: string, updates: Partial<ModifierGroup>): Promise<ModifierGroup> {
    const now = new Date().toISOString();
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    Object.entries(updates).forEach(([key, value]) => {
      if (key !== 'id' && key !== 'createdAt' && key !== 'updatedAt' && key !== 'modifiers') {
        const dbKey = this.camelToSnake(key);
        updateFields.push(`${dbKey} = ?`);
        if (typeof value === 'boolean') {
          updateValues.push(value ? 1 : 0);
        } else {
          updateValues.push(value);
        }
      }
    });

    if (updateFields.length > 0) {
      updateFields.push('updated_at = ?');
      updateValues.push(now, id);

      await this.sqliteService.run(
        `UPDATE modifier_groups SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );
    }

    return this.getModifierGroupById(id);
  }

  async deleteModifierGroup(id: string): Promise<void> {
    await this.sqliteService.run(
      'UPDATE modifier_groups SET is_active = 0, updated_at = ? WHERE id = ?',
      [new Date().toISOString(), id]
    );
  }

  // Modifier Management
  async createModifier(modifier: Omit<Modifier, 'createdAt' | 'updatedAt'>): Promise<Modifier> {
    const now = new Date().toISOString();
    await this.sqliteService.run(
      `INSERT INTO modifiers (
        id, modifier_group_id, name, price, description, is_available,
        display_order, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        modifier.id,
        modifier.modifierGroupId,
        modifier.name,
        modifier.price,
        modifier.description || null,
        modifier.isAvailable ? 1 : 0,
        modifier.displayOrder,
        now,
        now
      ]
    );

    return this.getModifierById(modifier.id);
  }

  async getModifiersByGroupId(groupId: string): Promise<Modifier[]> {
    const rows = await this.sqliteService.all(
      `SELECT * FROM modifiers
       WHERE modifier_group_id = ? AND is_available = 1
       ORDER BY display_order ASC, name ASC`,
      [groupId]
    );

    return rows.map(this.mapModifierFromDb);
  }

  async getModifierById(id: string): Promise<Modifier> {
    const row = await this.sqliteService.get(
      'SELECT * FROM modifiers WHERE id = ?',
      [id]
    );

    if (!row) {
      throw new Error('Modifier not found');
    }

    return this.mapModifierFromDb(row);
  }

  async updateModifier(id: string, updates: Partial<Modifier>): Promise<Modifier> {
    const now = new Date().toISOString();
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    Object.entries(updates).forEach(([key, value]) => {
      if (key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
        const dbKey = this.camelToSnake(key);
        updateFields.push(`${dbKey} = ?`);
        if (typeof value === 'boolean') {
          updateValues.push(value ? 1 : 0);
        } else {
          updateValues.push(value);
        }
      }
    });

    if (updateFields.length > 0) {
      updateFields.push('updated_at = ?');
      updateValues.push(now, id);

      await this.sqliteService.run(
        `UPDATE modifiers SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );
    }

    return this.getModifierById(id);
  }

  async deleteModifier(id: string): Promise<void> {
    await this.sqliteService.run(
      'UPDATE modifiers SET is_available = 0, updated_at = ? WHERE id = ?',
      [new Date().toISOString(), id]
    );
  }

  // Menu Item Modifier Group Association Management
  async assignModifierGroupToMenuItem(
    menuItemId: string,
    modifierGroupId: string,
    isRequired: boolean = false,
    displayOrder: number = 0
  ): Promise<MenuItemModifierGroup> {
    const id = `mimg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const now = new Date().toISOString();

    await this.sqliteService.run(
      `INSERT INTO menu_item_modifier_groups (
        id, menu_item_id, modifier_group_id, is_required, display_order, created_at
      ) VALUES (?, ?, ?, ?, ?, ?)`,
      [id, menuItemId, modifierGroupId, isRequired ? 1 : 0, displayOrder, now]
    );

    return this.getMenuItemModifierGroupById(id);
  }

  async getModifierGroupsForMenuItem(menuItemId: string): Promise<ModifierGroup[]> {
    const rows = await this.sqliteService.all(
      `SELECT mg.*, mimg.is_required as association_required, mimg.display_order as association_order
       FROM modifier_groups mg
       JOIN menu_item_modifier_groups mimg ON mg.id = mimg.modifier_group_id
       WHERE mimg.menu_item_id = ? AND mg.is_active = 1
       ORDER BY mimg.display_order ASC, mg.display_order ASC, mg.name ASC`,
      [menuItemId]
    );

    const groups = rows.map(row => {
      const group = this.mapModifierGroupFromDb(row);
      // Override isRequired with the association-specific value
      group.isRequired = row.association_required === 1;
      return group;
    });

    // Load modifiers for each group
    for (const group of groups) {
      group.modifiers = await this.getModifiersByGroupId(group.id);
    }

    return groups;
  }

  async getMenuItemModifierGroupById(id: string): Promise<MenuItemModifierGroup> {
    const row = await this.sqliteService.get(
      'SELECT * FROM menu_item_modifier_groups WHERE id = ?',
      [id]
    );

    if (!row) {
      throw new Error('Menu item modifier group association not found');
    }

    return this.mapMenuItemModifierGroupFromDb(row);
  }

  async removeModifierGroupFromMenuItem(menuItemId: string, modifierGroupId: string): Promise<void> {
    await this.sqliteService.run(
      'DELETE FROM menu_item_modifier_groups WHERE menu_item_id = ? AND modifier_group_id = ?',
      [menuItemId, modifierGroupId]
    );
  }

  // Mapping functions
  private mapModifierGroupFromDb(dbItem: any): ModifierGroup {
    return {
      id: dbItem.id,
      restaurantId: dbItem.restaurant_id,
      name: dbItem.name,
      description: dbItem.description,
      isRequired: dbItem.is_required === 1,
      minSelection: dbItem.min_selection,
      maxSelection: dbItem.max_selection,
      displayOrder: dbItem.display_order,
      isActive: dbItem.is_active === 1,
      createdAt: dbItem.created_at,
      updatedAt: dbItem.updated_at
    };
  }

  private mapModifierFromDb(dbItem: any): Modifier {
    return {
      id: dbItem.id,
      modifierGroupId: dbItem.modifier_group_id,
      name: dbItem.name,
      price: dbItem.price,
      description: dbItem.description,
      isAvailable: dbItem.is_available === 1,
      displayOrder: dbItem.display_order,
      createdAt: dbItem.created_at,
      updatedAt: dbItem.updated_at
    };
  }

  private mapMenuItemModifierGroupFromDb(dbItem: any): MenuItemModifierGroup {
    return {
      id: dbItem.id,
      menuItemId: dbItem.menu_item_id,
      modifierGroupId: dbItem.modifier_group_id,
      isRequired: dbItem.is_required === 1,
      displayOrder: dbItem.display_order,
      createdAt: dbItem.created_at
    };
  }

  // Update menu item popularity based on order frequency
  async updateMenuItemPopularity(orderItems: Omit<OrderItem, 'id' | 'orderId' | 'createdAt' | 'updatedAt'>[]): Promise<void> {
    try {
      for (const item of orderItems) {
        await this.sqliteService.run(
          `UPDATE menu_items
           SET popularity = popularity + ?
           WHERE id = ?`,
          [item.quantity, item.menuItemId]
        );
      }
    } catch (error) {
      console.error('Error updating menu item popularity:', error);
    }
  }

  // Order management
  async createOrder(orderData: Omit<Order, 'id' | 'orderNumber' | 'items' | 'createdAt' | 'updatedAt'>, items: Omit<OrderItem, 'id' | 'orderId' | 'createdAt' | 'updatedAt'>[]): Promise<Order> {
    return this.sqliteService.transaction(async () => {
      const orderId = `order_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Generate serialized order number with current date
      const now = new Date();
      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, ''); // YYYYMMDD

      // Get today's order count for serialization
      const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
      const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1).toISOString();

      const todayOrdersResult = await this.sqliteService.get(
        'SELECT COUNT(*) as count FROM orders WHERE created_at >= ? AND created_at < ? AND restaurant_id = ?',
        [todayStart, todayEnd, orderData.restaurantId]
      );

      const orderSequence = (todayOrdersResult?.count || 0) + 1;
      const orderNumber = `ORD-${dateStr}-${orderSequence.toString().padStart(4, '0')}`;
      const nowISO = now.toISOString();

      // Create order
      await this.sqliteService.run(
        `INSERT INTO orders (
          id, order_number, restaurant_id, table_id, table_number, order_type,
          status, subtotal, tax_amount, service_charge_amount, discount_amount,
          total_amount, payment_status, payment_method, customer_name,
          customer_phone, customer_email, notes, kot_printed, bill_printed,
          order_source, staff_id, device_id, session_id, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          orderId, orderNumber, orderData.restaurantId, orderData.tableId || null,
          orderData.tableNumber || null, orderData.orderType, orderData.status,
          orderData.subtotal, orderData.taxAmount, orderData.serviceChargeAmount,
          orderData.discountAmount, orderData.totalAmount, orderData.paymentStatus,
          orderData.paymentMethod || null, orderData.customerName || null,
          orderData.customerPhone || null, orderData.customerEmail || null,
          orderData.notes || null, orderData.kotPrinted ? 1 : 0,
          orderData.billPrinted ? 1 : 0, orderData.orderSource,
          orderData.staffId || null, orderData.deviceId || null,
          orderData.sessionId || null, nowISO, nowISO
        ]
      );

      // Create order items
      for (const item of items) {
        const itemId = `item_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        await this.sqliteService.run(
          `INSERT INTO order_items (
            id, order_id, menu_item_id, menu_item_name, menu_item_price,
            quantity, subtotal, notes, status, modifiers, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            itemId, orderId, item.menuItemId, item.menuItemName,
            item.menuItemPrice, item.quantity, item.subtotal,
            item.notes || null, item.status,
            item.modifiers ? JSON.stringify(item.modifiers) : null, nowISO, nowISO
          ]
        );
      }

      // Update menu item popularity
      await this.updateMenuItemPopularity(items);

      return this.getOrderById(orderId);
    });
  }

  async getOrderById(orderId: string): Promise<Order> {
    const order = await this.sqliteService.get<any>(
      'SELECT * FROM orders WHERE id = ?',
      [orderId]
    );

    if (!order) throw new Error('Order not found');

    const items = await this.sqliteService.all<any>(
      'SELECT * FROM order_items WHERE order_id = ? ORDER BY created_at',
      [orderId]
    );

    return this.mapOrderFromDb(order, items);
  }

  async getOrdersByRestaurant(restaurantId: string, filters?: {
    status?: string;
    tableId?: string;
    date?: string;
    limit?: number;
    offset?: number;
  }): Promise<Order[]> {
    let sql = 'SELECT * FROM orders WHERE restaurant_id = ?';
    const params: any[] = [restaurantId];

    if (filters?.status) {
      sql += ' AND status = ?';
      params.push(filters.status);
    }

    if (filters?.tableId) {
      sql += ' AND table_id = ?';
      params.push(filters.tableId);
    }

    if (filters?.date) {
      sql += ' AND DATE(created_at) = DATE(?)';
      params.push(filters.date);
    }

    sql += ' ORDER BY created_at DESC';

    if (filters?.limit) {
      sql += ' LIMIT ?';
      params.push(filters.limit);

      if (filters?.offset) {
        sql += ' OFFSET ?';
        params.push(filters.offset);
      }
    }

    const orders = await this.sqliteService.all<any>(sql, params);

    // Get items for each order
    const ordersWithItems: Order[] = [];
    for (const order of orders) {
      const items = await this.sqliteService.all<any>(
        'SELECT * FROM order_items WHERE order_id = ? ORDER BY created_at',
        [order.id]
      );
      ordersWithItems.push(this.mapOrderFromDb(order, items));
    }

    return ordersWithItems;
  }

  async updateOrderStatus(orderId: string, status: Order['status']): Promise<Order> {
    const now = new Date().toISOString();
    const updates: any = { status, updated_at: now };

    if (status === 'completed') {
      updates.completed_at = now;
    } else if (status === 'cancelled') {
      updates.cancelled_at = now;
    }

    const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);
    values.push(orderId);

    await this.sqliteService.run(
      `UPDATE orders SET ${setClause} WHERE id = ?`,
      values
    );

    return this.getOrderById(orderId);
  }

  async updateOrderItemStatus(itemId: string, status: OrderItem['status']): Promise<void> {
    const now = new Date().toISOString();
    const updates: any = { status, updated_at: now };

    if (status === 'preparing') {
      updates.preparation_start_time = now;
    } else if (status === 'ready') {
      updates.preparation_end_time = now;
    }

    const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);
    values.push(itemId);

    await this.sqliteService.run(
      `UPDATE order_items SET ${setClause} WHERE id = ?`,
      values
    );
  }

  async updateOrderItems(orderId: string, orderData: { items: OrderItem[], subtotal: number, taxAmount: number, totalAmount: number }): Promise<Order> {
    return this.sqliteService.transaction(async () => {
      const now = new Date().toISOString();

      // Delete existing order items
      await this.sqliteService.run('DELETE FROM order_items WHERE order_id = ?', [orderId]);

      // Insert updated order items
      for (const item of orderData.items) {
        const itemId = item.id.startsWith('temp_')
          ? `item_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
          : item.id;

        await this.sqliteService.run(
          `INSERT INTO order_items (
            id, order_id, menu_item_id, menu_item_name, menu_item_price,
            quantity, subtotal, notes, status, modifiers, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            itemId, orderId, item.menuItemId, item.menuItemName, item.menuItemPrice,
            item.quantity, item.subtotal, item.notes || null, item.status,
            item.modifiers ? JSON.stringify(item.modifiers) : null, now, now
          ]
        );
      }

      // Update order totals
      await this.sqliteService.run(
        `UPDATE orders SET
         subtotal = ?, tax_amount = ?, total_amount = ?, updated_at = ?
         WHERE id = ?`,
        [orderData.subtotal, orderData.taxAmount, orderData.totalAmount, now, orderId]
      );

      return this.getOrderById(orderId);
    });
  }

  // Table management
  async createTable(tableData: Omit<Table, 'createdAt' | 'updatedAt'>): Promise<Table> {
    const now = new Date().toISOString();
    await this.sqliteService.run(
      `INSERT INTO tables (
        id, restaurant_id, table_number, capacity, location, status,
        current_order_id, is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        tableData.id,
        tableData.restaurantId,
        tableData.tableNumber,
        tableData.capacity,
        tableData.location || null,
        tableData.status,
        tableData.currentOrderId || null,
        tableData.isActive ? 1 : 0,
        now,
        now
      ]
    );

    return this.getTableById(tableData.id);
  }

  async getTableById(tableId: string): Promise<Table> {
    const table = await this.sqliteService.get<any>(
      'SELECT * FROM tables WHERE id = ? AND is_active = 1',
      [tableId]
    );

    if (!table) throw new Error('Table not found');

    return this.mapTableFromDb(table);
  }

  async getTablesByRestaurant(restaurantId: string): Promise<Table[]> {
    const tables = await this.sqliteService.all<any>(
      'SELECT * FROM tables WHERE restaurant_id = ? AND is_active = 1 ORDER BY table_number',
      [restaurantId]
    );

    return tables.map(table => this.mapTableFromDb(table));
  }

  async updateTableStatus(tableId: string, status: Table['status'], currentOrderId?: string): Promise<Table> {
    await this.sqliteService.run(
      'UPDATE tables SET status = ?, current_order_id = ?, updated_at = ? WHERE id = ?',
      [status, currentOrderId || null, new Date().toISOString(), tableId]
    );

    return this.getTableById(tableId);
  }

  // Helper methods for mapping database rows to objects
  private mapOrderFromDb(dbOrder: any, dbItems: any[]): Order {
    return {
      id: dbOrder.id,
      orderNumber: dbOrder.order_number,
      restaurantId: dbOrder.restaurant_id,
      tableId: dbOrder.table_id,
      tableNumber: dbOrder.table_number,
      orderType: dbOrder.order_type,
      status: dbOrder.status,
      subtotal: dbOrder.subtotal,
      taxAmount: dbOrder.tax_amount,
      serviceChargeAmount: dbOrder.service_charge_amount,
      discountAmount: dbOrder.discount_amount,
      totalAmount: dbOrder.total_amount,
      paymentStatus: dbOrder.payment_status,
      paymentMethod: dbOrder.payment_method,
      customerName: dbOrder.customer_name,
      customerPhone: dbOrder.customer_phone,
      customerEmail: dbOrder.customer_email,
      notes: dbOrder.notes,
      kotPrinted: dbOrder.kot_printed === 1,
      billPrinted: dbOrder.bill_printed === 1,
      orderSource: dbOrder.order_source,
      staffId: dbOrder.staff_id,
      deviceId: dbOrder.device_id,
      sessionId: dbOrder.session_id,
      estimatedDeliveryTime: dbOrder.estimated_delivery_time,
      actualDeliveryTime: dbOrder.actual_delivery_time,
      rating: dbOrder.rating,
      feedback: dbOrder.feedback,
      items: dbItems.map(item => this.mapOrderItemFromDb(item)),
      createdAt: dbOrder.created_at,
      updatedAt: dbOrder.updated_at,
      completedAt: dbOrder.completed_at,
      cancelledAt: dbOrder.cancelled_at
    };
  }

  private mapOrderItemFromDb(dbItem: any): OrderItem {
    return {
      id: dbItem.id,
      orderId: dbItem.order_id,
      menuItemId: dbItem.menu_item_id,
      menuItemName: dbItem.menu_item_name,
      menuItemPrice: dbItem.menu_item_price,
      quantity: dbItem.quantity,
      subtotal: dbItem.subtotal,
      notes: dbItem.notes,
      status: dbItem.status,
      modifiers: dbItem.modifiers ? JSON.parse(dbItem.modifiers) : [],
      preparationStartTime: dbItem.preparation_start_time,
      preparationEndTime: dbItem.preparation_end_time,
      createdAt: dbItem.created_at,
      updatedAt: dbItem.updated_at
    };
  }

  private mapTableFromDb(dbTable: any): Table {
    return {
      id: dbTable.id,
      restaurantId: dbTable.restaurant_id,
      tableNumber: dbTable.table_number,
      capacity: dbTable.capacity,
      location: dbTable.location,
      status: dbTable.status,
      currentOrderId: dbTable.current_order_id,
      reservationCustomerName: dbTable.reservation_customer_name,
      reservationCustomerPhone: dbTable.reservation_customer_phone,
      reservationTime: dbTable.reservation_time,
      reservationPartySize: dbTable.reservation_party_size,
      isActive: dbTable.is_active === 1,
      createdAt: dbTable.created_at,
      updatedAt: dbTable.updated_at
    };
  }

  // Backup methods
  async createBackup(filename?: string): Promise<{ success: boolean; filename?: string; error?: string }> {
    return this.sqliteService.createBackup(filename);
  }

  async getBackups(): Promise<any[]> {
    return this.sqliteService.getBackups();
  }

  async restoreBackup(backupPath: string): Promise<{ success: boolean; error?: string }> {
    return this.sqliteService.restoreBackup(backupPath);
  }

  async healthCheck(): Promise<{ healthy: boolean; details: any }> {
    return this.sqliteService.healthCheck();
  }

  // Helper method to create sample menu items for new restaurants
  async createSampleMenuItems(restaurantId: string): Promise<void> {
    try {
      // Check if menu items already exist
      const existingItems = await this.sqliteService.get(
        'SELECT COUNT(*) as count FROM menu_items WHERE restaurant_id = ?',
        [restaurantId]
      );

      if (existingItems?.count > 0) {
        return; // Already has menu items
      }

      const sampleMenuItems = [
        {
          name: 'Margherita Pizza',
          price: 299.99,
          description: 'Fresh tomatoes, mozzarella, basil',
          category: 'Pizza',
          preparationTime: 15
        },
        {
          name: 'Caesar Salad',
          price: 199.99,
          description: 'Romaine lettuce, parmesan, croutons',
          category: 'Salads',
          preparationTime: 10
        },
        {
          name: 'Grilled Chicken',
          price: 349.99,
          description: 'Herb-seasoned grilled chicken breast',
          category: 'Main Course',
          preparationTime: 20
        },
        {
          name: 'Pasta Carbonara',
          price: 279.99,
          description: 'Creamy pasta with bacon and parmesan',
          category: 'Pasta',
          preparationTime: 18
        },
        {
          name: 'Fresh Orange Juice',
          price: 89.99,
          description: 'Freshly squeezed orange juice',
          category: 'Beverages',
          preparationTime: 5
        }
      ];

      for (const item of sampleMenuItems) {
        await this.createMenuItem({
          id: `sample_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          restaurantId,
          name: item.name,
          price: item.price,
          description: item.description,
          category: item.category,
          available: true,
          isDeleted: false,
          preparationTime: item.preparationTime,
          popularity: 0
        });
      }

      console.log('Sample menu items created for restaurant:', restaurantId);
    } catch (error) {
      console.error('Error creating sample menu items:', error);
    }
  }
}

// Singleton instance
let databaseService: DatabaseService | null = null;

export function getDatabaseService(): DatabaseService {
  if (!databaseService) {
    databaseService = new DatabaseService();
  }
  return databaseService;
}
