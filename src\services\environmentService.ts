import { app } from 'electron';
import * as path from 'path';
import * as fs from 'fs';

export interface AppConfig {
  email: {
    user: string;
    pass: string;
    service: string;
  };
  database: {
    path: string;
  };
  firebase: {
    apiKey: string;
    authDomain: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
    measurementId: string;
    privateKeyId: string;
    privateKey: string;
    clientEmail: string;
    clientId: string;
    clientCertUrl: string;
  };
  github: {
    token: string;
    owner: string;
    repo: string;
  };
  updater: {
    serverUrl: string;
    debug: boolean;
  };
}

class EnvironmentService {
  private config: AppConfig | null = null;
  private configPath: string;

  constructor() {
    // Get the user data directory for storing configuration
    const userDataPath = app.getPath('userData');
    this.configPath = path.join(userDataPath, 'app-config.json');
  }

  /**
   * Initialize the environment service
   */
  async initialize(): Promise<void> {
    try {
      // Try to load existing config
      await this.loadConfig();
    } catch (error) {
      console.log('No existing config found, creating default config');
      await this.createDefaultConfig();
    }
  }

  /**
   * Load configuration from file
   */
  private async loadConfig(): Promise<void> {
    if (fs.existsSync(this.configPath)) {
      const configData = fs.readFileSync(this.configPath, 'utf8');
      this.config = JSON.parse(configData);
      console.log('Configuration loaded from:', this.configPath);
    } else {
      throw new Error('Config file not found');
    }
  }

  /**
   * Create default configuration
   */
  private async createDefaultConfig(): Promise<void> {
    const userDataPath = app.getPath('userData');
    
    this.config = {
      email: {
        user: process.env.EMAIL_USER || '<EMAIL>',
        pass: process.env.EMAIL_PASS || 'mcig ozev gtbp nywu',
        service: 'gmail'
      },
      database: {
        path: process.env.DB_PATH || path.join(userDataPath, 'zyka.db')
      },
      firebase: {
        apiKey: process.env.FIREBASE_API_KEY || 'AIzaSyAGaKQjNLwn-4YfM-3g51wZn3_s4aTYZNg',
        authDomain: process.env.FIREBASE_AUTH_DOMAIN || 'zyka-pos.firebaseapp.com',
        projectId: process.env.FIREBASE_PROJECT_ID || 'zyka-pos',
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET || 'zyka-pos.firebasestorage.app',
        messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID || '462345618187',
        appId: process.env.FIREBASE_APP_ID || '1:462345618187:web:a8f7b57272716e3560980e',
        measurementId: process.env.FIREBASE_MEASUREMENT_ID || 'G-WTJ0762LK3',
        privateKeyId: process.env.FIREBASE_PRIVATE_KEY_ID || '3359c0736cec62974dfbf7dc85d7666c9537b11b',
        privateKey: process.env.FIREBASE_PRIVATE_KEY || '"-----BEGIN PRIVATE KEY-----\\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCvPbkh5g8n0J52\\ndIC0SnoeiKdNgVsPh3ugAYK0/uMKvrUolbS1nMqKbvMRJ7X/JF9rPBmvGB0I4UuN\\nf9imdSdwJq6xG8QfD9T9hdyV/4TT41SIU+jgwB4k0o1Vu5eZbtfUUQRGO5i68SMk\\nQzqlNya0VlFh0L/fwH8yqqQ8GYRK/4PuXh524zQsKseC3ppHv7oeUvV6Q2HaYo6i\\ngNuz+I5AOmqIGPXIYWM8zZGEPE1XKlnewcAUmvgrmLiH82MJoxrVcNrr8wQau7kY\\nraARVEpKlVq8fkFdBswRTOos8Bk621CqvjuskydvBr97mTMfN1c+QRNDQXsSfPAe\\nPSCcNsXPAgMBAAECggEABdLlTi0ZH9edDI6+oD9BY7Zi6P59ijyUbTLfpyg5X2gL\\nGTgwc0CssoERbuaE89+/lA7BQ8JnZJ4oJqGzD4UhNGb0s0oa8HVUPMCwHgQJ5AjQ\\nDF/Ng3qfBdZBgDHlzeGTJqkynSrp3nyZEGFzl34HuUKNHdUUK1l4YLSvFJg8f0mE\\nfmw5VvM9WPYzLLiUVIu4keJdFElNkDMSv+TFueh81NWcWURlaIKcMjRSr3Fybtdm\\n9gTx7vXgF9zfBwgl6LiPl73xExm90qPNkVe+EfSkRgNdBlzVuPuk22HeVYtyWTOO\\nhA9fD2gg2/ru0uebduXxjzcVG54ZFmUobwOPCs/XwQKBgQDepyN2g1es6XfcshFl\\n8QteBz3M4cEwJaBeK2KogyS3QiYeDjVeAmCjkMe/6ep0RjVJwg574/sp1eeCq6d2\\n3stIRh3i4zLBWnlPGK20oUZkDDQSnH1SgPkaQd+ZdMXI0TgTbpxq+c+wxza21zvm\\noxhGJW8jOfwpRn9etYLZa1BdEQKBgQDJfL4XteLxQc/Q2UsVLRb2HNmSEcOQRBTT\\nQsBy0SAnO9pUmcWol7C9Oba6W4nPoRRZ/p8OIhJmdgUHU5P7HDaFp1tV8WHiy1oh\\n7nMtS4iVTMMv8bWys3WeLZuotSKRsCwWffeaxiKjiuwmC7+Em+0tK40FlMPxl4oM\\nGYHaTmp03wKBgARuHu6EvoHaQ2BIyr/Ug/1i93/UHhvvI7m780KXGWvsDQYcEkPE\\nmFeo0dCnvtqBax4ElnUwnBbBAtI+yB49YJ4XhxC0pJXkTvWb8Bjf/J6Q/5g/NN8n\\nYc178g8Dm8vbHdqFIOegANnrnNMWhUKZjCoZJJT22mWX2YDw66GX2QmBAoGBAINV\\nGhseZcTYHvuFB5nQAC1/9JlvlseMeP6TfhnZElpAezm12c7gwUU2P1MNo7CA0GEw\\nxMBTmSbMOs7hvY1qgGdyBOjqI+HOpvuDD/QwTeDzXi78IHFQ5JOsKtA28UeBS9uF\\nVEXAbIepX/AUrEtlz50nzA4dP/NT0Zm9bpS7lEyFAoGAFpTM972h8np8bFxJZZFv\\nx/lW/hKPTtnvPleTD+PftSc73ZXm2pv3hfIVcXSoV1dnj8zkg+rUjKq1BfGDiA6M\\nzj4Ng0Yj4m2EZglucWuXs/cwfR4RYKhQXvkgGgdszr4dExiSL93STnIkX9uRe+jT\\nuR5bNsc01MM+CJF7C/ddnqo=\\n-----END PRIVATE KEY-----"',
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL || '<EMAIL>',
        clientId: process.env.FIREBASE_CLIENT_ID || '105681314098969927862',
        clientCertUrl: process.env.FIREBASE_CLIENT_CERT_URL || 'https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40zyka-pos.iam.gserviceaccount.com'
      },
      github: {
        token: process.env.GH_TOKEN || process.env.GITHUB_TOKEN || '',
        owner: process.env.GITHUB_OWNER || 'EriaSoftware',
        repo: process.env.GITHUB_REPO || 'zyka_pos'
      },
      updater: {
        serverUrl: process.env.UPDATE_SERVER_URL || 'https://zyka-pos-updates.pages.dev',
        debug: process.env.ELECTRON_UPDATER_DEBUG === 'true'
      }
    };

    await this.saveConfig();
    console.log('Default configuration created at:', this.configPath);
  }

  /**
   * Save configuration to file
   */
  private async saveConfig(): Promise<void> {
    if (!this.config) {
      throw new Error('No configuration to save');
    }

    const configDir = path.dirname(this.configPath);
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }

    fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
  }

  /**
   * Get configuration
   */
  getConfig(): AppConfig {
    if (!this.config) {
      throw new Error('Configuration not initialized');
    }
    return this.config;
  }

  /**
   * Update configuration
   */
  async updateConfig(updates: Partial<AppConfig>): Promise<void> {
    if (!this.config) {
      throw new Error('Configuration not initialized');
    }

    this.config = { ...this.config, ...updates };
    await this.saveConfig();
  }

  /**
   * Get email configuration
   */
  getEmailConfig() {
    return this.getConfig().email;
  }

  /**
   * Get database path
   */
  getDatabasePath(): string {
    return this.getConfig().database.path;
  }

  /**
   * Get Firebase configuration
   */
  getFirebaseConfig() {
    return this.getConfig().firebase;
  }

  /**
   * Check if email is configured
   */
  isEmailConfigured(): boolean {
    const emailConfig = this.getEmailConfig();
    return !!(emailConfig.user && emailConfig.pass && 
             emailConfig.user !== '<EMAIL>' &&
             emailConfig.pass !== 'your-app-password');
  }

  /**
   * Get configuration file path
   */
  getConfigPath(): string {
    return this.configPath;
  }
}

// Singleton instance
let environmentService: EnvironmentService | null = null;

export function getEnvironmentService(): EnvironmentService {
  if (!environmentService) {
    environmentService = new EnvironmentService();
  }
  return environmentService;
}

export { EnvironmentService };
