import { getDatabaseService } from './databaseService';
import { <PERSON><PERSON>erWindow } from 'electron';

const databaseService = getDatabaseService();

export interface NotificationData {
  userId: string;
  restaurantId?: number;
  type: 'orders' | 'tables' | 'bills' | 'menu' | 'tax' | 'system';
  title: string;
  message: string;
  data?: any;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  autoDismiss?: boolean;
  dismissAfter?: number;
}

export class NotificationService {
  private static instance: NotificationService;

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  async createNotification(notificationData: NotificationData): Promise<void> {
    try {
      // Check if notifications are enabled for this user and category
      const preferences = await databaseService.sqliteService.get(
        `
        SELECT enabled FROM notification_preferences 
        WHERE user_id = ? AND category = ?
      `,
        [notificationData.userId, notificationData.type]
      );

      // If preferences exist and notifications are disabled, skip
      if (preferences && !preferences.enabled) {
        return;
      }

      const notificationId = `notif_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const now = new Date().toISOString();

      await databaseService.sqliteService.run(
        `
        INSERT INTO notifications (
          id, user_id, restaurant_id, type, title, message, data,
          read_status, priority, auto_dismiss, dismiss_after, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        [
          notificationId,
          notificationData.userId,
          notificationData.restaurantId || null,
          notificationData.type,
          notificationData.title,
          notificationData.message,
          notificationData.data ? JSON.stringify(notificationData.data) : null,
          0, // read_status = false
          notificationData.priority || 'normal',
          notificationData.autoDismiss ? 1 : 0,
          notificationData.dismissAfter || 0,
          now,
        ]
      );

      const notification = await databaseService.sqliteService.get(
        'SELECT * FROM notifications WHERE id = ?',
        [notificationId]
      );

      const formattedNotification = {
        id: notification.id,
        userId: notification.user_id,
        restaurantId: notification.restaurant_id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data ? JSON.parse(notification.data) : null,
        readStatus: Boolean(notification.read_status),
        priority: notification.priority,
        autoDismiss: Boolean(notification.auto_dismiss),
        dismissAfter: notification.dismiss_after,
        createdAt: notification.created_at,
        readAt: notification.read_at,
      };

      // Emit notification event to all renderer processes
      BrowserWindow.getAllWindows().forEach((window: any) => {
        window.webContents.send('notification-received', formattedNotification);
      });
    } catch (error) {
      console.error('Error creating notification:', error);
    }
  }

  // Order-related notifications
  async notifyNewOrder(
    userId: string,
    restaurantId: number,
    orderNumber: string,
    tableNumber?: string
  ): Promise<void> {
    await this.createNotification({
      userId,
      restaurantId,
      type: 'orders',
      title: 'New Order Received',
      message: `Order ${orderNumber}${tableNumber ? ` for Table ${tableNumber}` : ''} has been placed`,
      data: { orderNumber, tableNumber },
      priority: 'high',
      autoDismiss: false,
    });
  }

  async notifyOrderStatusChange(
    userId: string,
    restaurantId: number,
    orderNumber: string,
    status: string,
    tableNumber?: string
  ): Promise<void> {
    const statusMessages = {
      confirmed: 'has been confirmed',
      preparing: 'is being prepared',
      ready: 'is ready for serving',
      served: 'has been served',
      completed: 'has been completed',
      cancelled: 'has been cancelled',
    };

    await this.createNotification({
      userId,
      restaurantId,
      type: 'orders',
      title: 'Order Status Update',
      message: `Order ${orderNumber}${tableNumber ? ` (Table ${tableNumber})` : ''} ${statusMessages[status as keyof typeof statusMessages] || `status changed to ${status}`}`,
      data: { orderNumber, status, tableNumber },
      priority: status === 'ready' ? 'high' : 'normal',
      autoDismiss: true,
      dismissAfter: 10,
    });
  }

  // Table-related notifications
  async notifyTableStatusChange(
    userId: string,
    restaurantId: number,
    tableNumber: string,
    status: string
  ): Promise<void> {
    const statusMessages = {
      occupied: 'is now occupied',
      available: 'is now available',
      reserved: 'has been reserved',
      maintenance: 'is under maintenance',
    };

    await this.createNotification({
      userId,
      restaurantId,
      type: 'tables',
      title: 'Table Status Update',
      message: `Table ${tableNumber} ${statusMessages[status as keyof typeof statusMessages] || `status changed to ${status}`}`,
      data: { tableNumber, status },
      priority: 'normal',
      autoDismiss: true,
      dismissAfter: 8,
    });
  }

  async notifyReservation(
    userId: string,
    restaurantId: number,
    tableNumber: string,
    customerName: string,
    reservationTime: string
  ): Promise<void> {
    await this.createNotification({
      userId,
      restaurantId,
      type: 'tables',
      title: 'New Reservation',
      message: `Table ${tableNumber} reserved by ${customerName} for ${new Date(reservationTime).toLocaleString()}`,
      data: { tableNumber, customerName, reservationTime },
      priority: 'normal',
      autoDismiss: false,
    });
  }

  // Bill and payment notifications
  async notifyPaymentReceived(
    userId: string,
    restaurantId: number,
    orderNumber: string,
    amount: number,
    paymentMethod: string
  ): Promise<void> {
    await this.createNotification({
      userId,
      restaurantId,
      type: 'bills',
      title: 'Payment Received',
      message: `Payment of ₹${amount.toFixed(2)} received for Order ${orderNumber} via ${paymentMethod}`,
      data: { orderNumber, amount, paymentMethod },
      priority: 'normal',
      autoDismiss: true,
      dismissAfter: 8,
    });
  }

  async notifyBillPrinted(
    userId: string,
    restaurantId: number,
    orderNumber: string
  ): Promise<void> {
    await this.createNotification({
      userId,
      restaurantId,
      type: 'bills',
      title: 'Bill Printed',
      message: `Bill for Order ${orderNumber} has been printed`,
      data: { orderNumber },
      priority: 'low',
      autoDismiss: true,
      dismissAfter: 5,
    });
  }

  async notifyPaymentOverdue(
    userId: string,
    restaurantId: number,
    orderNumber: string,
    amount: number
  ): Promise<void> {
    await this.createNotification({
      userId,
      restaurantId,
      type: 'bills',
      title: 'Payment Overdue',
      message: `Payment of ₹${amount.toFixed(2)} for Order ${orderNumber} is overdue`,
      data: { orderNumber, amount },
      priority: 'urgent',
      autoDismiss: false,
    });
  }

  // Menu-related notifications
  async notifyMenuItemUpdate(
    userId: string,
    restaurantId: number,
    itemName: string,
    action: string
  ): Promise<void> {
    const actionMessages = {
      added: 'has been added to the menu',
      updated: 'has been updated',
      removed: 'has been removed from the menu',
      unavailable: 'is now unavailable',
      available: 'is now available',
    };

    await this.createNotification({
      userId,
      restaurantId,
      type: 'menu',
      title: 'Menu Update',
      message: `${itemName} ${actionMessages[action as keyof typeof actionMessages] || action}`,
      data: { itemName, action },
      priority: 'normal',
      autoDismiss: true,
      dismissAfter: 8,
    });
  }

  async notifyLowStock(userId: string, restaurantId: number, itemName: string): Promise<void> {
    await this.createNotification({
      userId,
      restaurantId,
      type: 'menu',
      title: 'Low Stock Alert',
      message: `${itemName} is running low on stock`,
      data: { itemName },
      priority: 'high',
      autoDismiss: false,
    });
  }

  // Tax-related notifications
  async notifyTaxRateChange(
    userId: string,
    restaurantId: number,
    taxName: string,
    oldRate: number,
    newRate: number
  ): Promise<void> {
    await this.createNotification({
      userId,
      restaurantId,
      type: 'tax',
      title: 'Tax Rate Updated',
      message: `${taxName} rate changed from ${oldRate}% to ${newRate}%`,
      data: { taxName, oldRate, newRate },
      priority: 'normal',
      autoDismiss: true,
      dismissAfter: 10,
    });
  }

  // System notifications
  async notifySystemUpdate(userId: string, title: string, message: string): Promise<void> {
    await this.createNotification({
      userId,
      type: 'system',
      title,
      message,
      priority: 'normal',
      autoDismiss: false,
    });
  }

  async notifySystemError(userId: string, error: string, details?: string): Promise<void> {
    await this.createNotification({
      userId,
      type: 'system',
      title: 'System Error',
      message: `${error}${details ? `: ${details}` : ''}`,
      data: { error, details },
      priority: 'urgent',
      autoDismiss: false,
    });
  }

  async notifyBackupStatus(userId: string, success: boolean, filename?: string): Promise<void> {
    await this.createNotification({
      userId,
      type: 'system',
      title: success ? 'Backup Completed' : 'Backup Failed',
      message: success
        ? `Database backup created successfully${filename ? `: ${filename}` : ''}`
        : 'Failed to create database backup',
      data: { success, filename },
      priority: success ? 'low' : 'high',
      autoDismiss: success,
      dismissAfter: success ? 8 : 0,
    });
  }
}

export const notificationService = NotificationService.getInstance();
